"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { 
  trackPageView, 
  trackPurchase, 
  trackLead, 
  trackCompleteRegistration, 
  trackViewContent,
  trackAddToCart,
  trackInitiateCheckout 
} from "@/lib/meta-pixel"

declare global {
  interface Window {
    fbq: any
  }
}

export default function TestPixelPage() {
  const [pixelStatus, setPixelStatus] = useState<'loading' | 'loaded' | 'error'>('loading')
  const [events, setEvents] = useState<string[]>([])

  useEffect(() => {
    // Check if Meta Pixel is loaded
    const checkPixel = () => {
      if (typeof window !== 'undefined') {
        if (window.fbq) {
          setPixelStatus('loaded')
          addEvent('Meta Pixel loaded successfully')
        } else {
          setPixelStatus('error')
          addEvent('Meta Pixel failed to load')
        }
      }
    }

    // Check immediately and after a delay
    checkPixel()
    const timer = setTimeout(checkPixel, 2000)

    return () => clearTimeout(timer)
  }, [])

  const addEvent = (event: string) => {
    const timestamp = new Date().toLocaleTimeString()
    setEvents(prev => [`[${timestamp}] ${event}`, ...prev])
  }

  const testEvents = [
    {
      name: 'PageView',
      description: 'Track a page view event',
      action: () => {
        trackPageView()
        addEvent('PageView event triggered')
      }
    },
    {
      name: 'Purchase',
      description: 'Track a purchase event ($99.99)',
      action: () => {
        trackPurchase(99.99, 'USD')
        addEvent('Purchase event triggered ($99.99 USD)')
      }
    },
    {
      name: 'Lead',
      description: 'Track a lead generation event',
      action: () => {
        trackLead()
        addEvent('Lead event triggered')
      }
    },
    {
      name: 'CompleteRegistration',
      description: 'Track a registration completion',
      action: () => {
        trackCompleteRegistration()
        addEvent('CompleteRegistration event triggered')
      }
    },
    {
      name: 'ViewContent',
      description: 'Track content view',
      action: () => {
        trackViewContent('Test Content')
        addEvent('ViewContent event triggered')
      }
    },
    {
      name: 'AddToCart',
      description: 'Track add to cart ($50.00)',
      action: () => {
        trackAddToCart(50.00, 'USD')
        addEvent('AddToCart event triggered ($50.00 USD)')
      }
    },
    {
      name: 'InitiateCheckout',
      description: 'Track checkout initiation ($75.00)',
      action: () => {
        trackInitiateCheckout(75.00, 'USD')
        addEvent('InitiateCheckout event triggered ($75.00 USD)')
      }
    }
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#002a3c] via-[#003a4c] to-[#001a2c] text-white p-8">
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold mb-4">Meta Pixel Test Page</h1>
          <p className="text-gray-300">Test and debug Meta Pixel implementation</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Status Card */}
          <Card className="bg-gray-900/50 border-gray-700">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                Meta Pixel Status
                <Badge variant={pixelStatus === 'loaded' ? 'default' : pixelStatus === 'error' ? 'destructive' : 'secondary'}>
                  {pixelStatus}
                </Badge>
              </CardTitle>
              <CardDescription>
                Current status of Meta Pixel (ID: 1287306579402842)
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <strong>Pixel ID:</strong> 1287306579402842
                </div>
                <div>
                  <strong>Status:</strong> {pixelStatus}
                </div>
                <div>
                  <strong>fbq Available:</strong> {typeof window !== 'undefined' && window.fbq ? 'Yes' : 'No'}
                </div>
                {typeof window !== 'undefined' && window.fbq && (
                  <div>
                    <strong>fbq Version:</strong> {window.fbq.version || 'Unknown'}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Test Events Card */}
          <Card className="bg-gray-900/50 border-gray-700">
            <CardHeader>
              <CardTitle>Test Events</CardTitle>
              <CardDescription>
                Click buttons to test different Meta Pixel events
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 gap-3">
                {testEvents.map((event) => (
                  <Button
                    key={event.name}
                    onClick={event.action}
                    variant="outline"
                    className="justify-start text-left h-auto p-4 border-gray-600 hover:border-sky-500"
                    disabled={pixelStatus !== 'loaded'}
                  >
                    <div>
                      <div className="font-semibold">{event.name}</div>
                      <div className="text-sm text-gray-400">{event.description}</div>
                    </div>
                  </Button>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Event Log Card */}
          <Card className="bg-gray-900/50 border-gray-700 lg:col-span-2">
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                Event Log
                <Button 
                  onClick={() => setEvents([])} 
                  variant="outline" 
                  size="sm"
                  className="border-gray-600"
                >
                  Clear Log
                </Button>
              </CardTitle>
              <CardDescription>
                Real-time log of Meta Pixel events
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="bg-black/50 rounded-lg p-4 h-64 overflow-y-auto">
                {events.length === 0 ? (
                  <p className="text-gray-500 italic">No events logged yet...</p>
                ) : (
                  <div className="space-y-1">
                    {events.map((event, index) => (
                      <div key={index} className="text-sm font-mono text-green-400">
                        {event}
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Instructions */}
        <Card className="bg-gray-900/50 border-gray-700 mt-8">
          <CardHeader>
            <CardTitle>How to Verify Meta Pixel</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4 text-sm">
              <div>
                <strong>1. Browser Developer Tools:</strong>
                <p className="text-gray-300">Open browser dev tools (F12) → Network tab → Filter by "facebook" to see pixel requests</p>
              </div>
              <div>
                <strong>2. Meta Pixel Helper Extension:</strong>
                <p className="text-gray-300">Install the Meta Pixel Helper Chrome extension to see pixel events in real-time</p>
              </div>
              <div>
                <strong>3. Facebook Events Manager:</strong>
                <p className="text-gray-300">Check your Facebook Business Manager → Events Manager to see incoming events</p>
              </div>
              <div>
                <strong>4. Console Commands:</strong>
                <p className="text-gray-300">In browser console, type: <code className="bg-black/50 px-2 py-1 rounded">window.fbq</code> to check if pixel is loaded</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
