"use client"

import type React from "react"

import { useState } from "react"
import { motion } from "framer-motion"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { AlertCircle, CheckCircle, Clock, FileText, Upload, User, X } from "lucide-react"
import { Progress } from "@/components/ui/progress"

export default function KycPage() {
  const [activeTab, setActiveTab] = useState("personal")
  const [kycStatus, setKycStatus] = useState("pending") // pending, in-review, approved, rejected
  const [uploadedFiles, setUploadedFiles] = useState<{ name: string; size: string; status: string }[]>([])

  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files
    if (files) {
      const newFiles = Array.from(files).map((file) => ({
        name: file.name,
        size: `${(file.size / 1024 / 1024).toFixed(2)} MB`,
        status: "uploaded",
      }))
      setUploadedFiles([...uploadedFiles, ...newFiles])
    }
  }

  const removeFile = (index: number) => {
    const newFiles = [...uploadedFiles]
    newFiles.splice(index, 1)
    setUploadedFiles(newFiles)
  }

  const fadeInUp = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.5 } },
  }

  return (
    <div className="space-y-8">
      {/* Trading Challenge Requirement Card */}
      <motion.div variants={fadeInUp} initial="hidden" animate="visible">
        <Card className="bg-gradient-to-r from-teal-500/10 to-blue-500/10 border-teal-500/30">
          <CardHeader>
            <div className="flex items-center">
              <CheckCircle className="h-6 w-6 text-teal-500 mr-3" />
              <div>
                <CardTitle className="text-white">Trading Challenge Requirement</CardTitle>
                <CardDescription className="text-gray-300">
                  Pass your trading challenge to unlock KYC verification
                </CardDescription>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-teal-500 rounded-full mt-2"></div>
                <div>
                  <p className="text-white font-medium">Complete Your Trading Challenge</p>
                  <p className="text-gray-400 text-sm">
                    Successfully pass your chosen trading challenge (Instant, HFT, Step 1 & 2) to demonstrate your trading skills and risk management abilities.
                  </p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-teal-500 rounded-full mt-2"></div>
                <div>
                  <p className="text-white font-medium">Meet Profit Targets</p>
                  <p className="text-gray-400 text-sm">
                    Achieve the required profit targets while adhering to our risk management rules and drawdown limits.
                  </p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-teal-500 rounded-full mt-2"></div>
                <div>
                  <p className="text-white font-medium">Unlock KYC Access</p>
                  <p className="text-gray-400 text-sm">
                    Once you pass your trading challenge, KYC verification becomes available to complete your account setup and enable withdrawals.
                  </p>
                </div>
              </div>
            </div>
            <div className="mt-6 p-4 bg-yellow-500/10 border border-yellow-500/30 rounded-lg">
              <div className="flex items-center">
                <AlertCircle className="h-5 w-5 text-yellow-500 mr-2" />
                <p className="text-yellow-400 font-medium">Important Note</p>
              </div>
              <p className="text-gray-300 text-sm mt-2">
                KYC verification is only available after successfully completing your trading challenge. This ensures that only qualified traders proceed to the funded account stage.
              </p>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      <motion.div variants={fadeInUp} initial="hidden" animate="visible" transition={{ delay: 0.2 }}>
        <Card className="bg-[#002a3c] border-[#003a4c]">
          <CardHeader>
            <CardTitle className="text-white">Verification Requirements</CardTitle>
            <CardDescription className="text-gray-400">Guidelines for successful identity verification</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="space-y-3">
                <div className="flex items-center">
                  <User className="h-5 w-5 text-teal-500 mr-2" />
                  <h3 className="text-white font-medium">Identity Document</h3>
                </div>
                <ul className="space-y-2 text-sm text-gray-400">
                  <li className="flex items-start">
                    <span className="mr-2">•</span>
                    <span>Government-issued ID (passport, driver's license, national ID)</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2">•</span>
                    <span>Document must be valid and not expired</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2">•</span>
                    <span>All corners and edges must be visible</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2">•</span>
                    <span>Information must be clearly readable</span>
                  </li>
                </ul>
              </div>

              <div className="space-y-3">
                <div className="flex items-center">
                  <FileText className="h-5 w-5 text-teal-500 mr-2" />
                  <h3 className="text-white font-medium">Proof of Address</h3>
                </div>
                <ul className="space-y-2 text-sm text-gray-400">
                  <li className="flex items-start">
                    <span className="mr-2">•</span>
                    <span>Utility bill, bank statement, or official letter</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2">•</span>
                    <span>Must be less than 3 months old</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2">•</span>
                    <span>Must show your full name and address</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2">•</span>
                    <span>Digital bills are acceptable if they show the full page</span>
                  </li>
                </ul>
              </div>

              <div className="space-y-3">
                <div className="flex items-center">
                  <AlertCircle className="h-5 w-5 text-teal-500 mr-2" />
                  <h3 className="text-white font-medium">Important Notes</h3>
                </div>
                <ul className="space-y-2 text-sm text-gray-400">
                  <li className="flex items-start">
                    <span className="mr-2">•</span>
                    <span>Verification usually takes 1-2 business days</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2">•</span>
                    <span>All information must match your account details</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2">•</span>
                    <span>Documents must be in color (not black and white)</span>
                  </li>
                  <li className="flex items-start">
                    <span className="mr-2">•</span>
                    <span>File size must be under 5MB per document</span>
                  </li>
                </ul>
              </div>
            </div>
          </CardContent>
          <CardFooter className="border-t border-[#003a4c] pt-6">
            <p className="text-sm text-gray-400">
              For any questions regarding the verification process, please contact our support team at{" "}
              <a href="mailto:<EMAIL>" className="text-teal-400 hover:underline">
                <EMAIL>
              </a>
            </p>
          </CardFooter>
        </Card>
      </motion.div>
    </div>
  )
}
