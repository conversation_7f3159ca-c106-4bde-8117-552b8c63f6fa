"use client"

import { motion } from "framer-motion"
import { <PERSON>, CardContent } from "@/components/ui/card"
import { Star, Crown, Diamond, Quote, Award, TrendingUp, CheckCircle, DollarSign, Calendar, MapPin, Clock, TrendingDown } from "lucide-react"
import { Carousel, CarouselContent, CarouselItem, CarouselNext, CarouselPrevious } from "@/components/ui/carousel"
import Autoplay from "embla-carousel-autoplay"

export default function TestimonialsSection() {
  const testimonials = [
    {
      name: "<PERSON>",
      role: "Forex Trader",
      content:
        "Clear rules, fair evaluation, and fast payouts. Funded Whales helped me level up my trading.",
      image: "https://randomuser.me/api/portraits/men/32.jpg",
      location: "New York, USA",
      verified: true,
    },
    {
      name: "<PERSON><PERSON>",
      role: "Forex Trader",
      content:
        "Transparent, professional, and supportive. I passed my challenge and joined a great community.",
      image: "https://randomuser.me/api/portraits/women/44.jpg",
      location: "London, UK",
      verified: true,
    },
    {
      name: "<PERSON>",
      role: "Forex Trader",
      content:
        "Reliable platform and always on-time payouts. Highly recommend for serious traders.",
      image: "https://randomuser.me/api/portraits/men/65.jpg",
      location: "Toronto, Canada",
      verified: true,
    },
    {
      name: "Emily Chen",
      role: "Forex Trader",
      content:
        "Challenging but fair. No hidden fees and great support. I trust Funded Whales.",
      image: "https://randomuser.me/api/portraits/women/68.jpg",
      location: "Chicago, USA",
      verified: true,
    },
    {
      name: "Ahmed Hassan",
      role: "Forex Trader",
      content:
        "Great for algo traders. Transparent profit sharing and stable execution.",
      image: "https://randomuser.me/api/portraits/men/43.jpg",
      location: "Singapore",
      verified: true,
    },
    {
      name: "Sofia Rossi",
      role: "Forex Trader",
      content:
        "Easy process, clear rules, and helpful support. My payouts are always on time.",
      image: "https://randomuser.me/api/portraits/women/65.jpg",
      location: "Sydney, Australia",
      verified: true,
    },
  ]

  return (
    <section id="testimonials" className="py-20 bg-gradient-to-b from-[#001a2c] via-[#001e30] to-[#002235] relative overflow-hidden">
      {/* Luxury background elements */}
      <div className="absolute inset-0 pointer-events-none">
        <div className="absolute top-10 left-10 w-80 h-80 bg-sky-400/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-10 right-10 w-96 h-96 bg-blue-400/10 rounded-full blur-3xl"></div>
      </div>

      {/* Floating luxury elements */}
      <div className="absolute inset-0 overflow-hidden">
        {[...Array(25)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-1.5 h-1.5 bg-sky-400/20 rounded-full"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              y: [-25, -100],
              opacity: [0, 0.7, 0],
              scale: [0.4, 1.1, 0.4],
            }}
            transition={{
              duration: 7 + Math.random() * 5,
              repeat: Number.POSITIVE_INFINITY,
              delay: Math.random() * 7,
              ease: "easeInOut",
            }}
          />
        ))}
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
          className="text-center mb-20"
        >
          <div className="flex items-center justify-center mb-4">
            <span className="text-sky-400 font-medium tracking-wider uppercase text-sm flex items-center gap-2">
              <CheckCircle className="h-4 w-4" />
              Verified Trader Stories
            </span>
          </div>
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-4 text-white">
            What Our{" "}
            <span className="bg-gradient-to-r from-sky-400 to-blue-400 bg-clip-text text-transparent">
              Traders Say
            </span>
          </h2>
          <p className="text-lg text-gray-300 max-w-3xl mx-auto leading-relaxed">
            Real feedback from verified traders who have successfully completed our evaluation process and are actively trading with funded accounts.
          </p>
        </motion.div>

        <Carousel
          opts={{ align: "start", loop: true }}
          plugins={[Autoplay({ delay: 8000 })]}
          className="w-full max-w-4xl mx-auto"
        >
          <CarouselContent>
            {testimonials.map((testimonial, index) => (
              <CarouselItem key={index} className="py-8">
                <motion.div
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="flex flex-col md:flex-row items-center md:items-stretch gap-0 md:gap-8 w-full"
                >
                  {/* Image left, card right */}
                  <div className="flex-shrink-0 flex justify-center items-center w-full md:w-64">
                    <img
                      src={testimonial.image}
                      alt={testimonial.name}
                      className="w-full md:w-64 h-72 md:h-80 object-cover rounded-2xl shadow-xl border-4 border-white"
                    />
                  </div>
                  <Card className="flex-1 bg-gradient-to-br from-[#002a3c]/90 to-[#001e30]/90 border border-sky-500/20 rounded-2xl shadow-xl p-8 flex flex-col justify-center mt-6 md:mt-0">
                    {/* Verified badge */}
                    <div className="flex items-center gap-2 mb-2">
                      <span className="font-bold text-xl text-white">{testimonial.name}</span>
                    {testimonial.verified && (
                        <span className="bg-sky-500/20 text-sky-400 px-2 py-1 rounded-full text-xs font-semibold flex items-center gap-1"><CheckCircle className="h-4 w-4" />Verified</span>
                      )}
                      </div>
                    <span className="text-sky-400 font-medium">{testimonial.role}</span>
                    <span className="text-gray-400 text-sm mb-4">{testimonial.location}</span>
                    <p className="text-lg text-sky-100 italic border-l-4 border-sky-400 pl-4">"{testimonial.content}"</p>
                  </Card>
                </motion.div>
              </CarouselItem>
            ))}
          </CarouselContent>
          <CarouselPrevious className="bg-white/80 border-sky-400/30 text-sky-400 hover:bg-sky-100" />
          <CarouselNext className="bg-white/80 border-sky-400/30 text-sky-400 hover:bg-sky-100" />
        </Carousel>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, delay: 0.6 }}
          className="text-center mt-16"
        >
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 max-w-5xl mx-auto">
            <div className="bg-gradient-to-br from-sky-500/10 to-blue-500/10 border border-sky-400/20 rounded-xl px-6 py-6">
              <div className="text-3xl font-bold text-sky-400 mb-2">$850K+</div>
              <div className="text-gray-300 text-sm">Total Profits Paid</div>
            </div>
            <div className="bg-gradient-to-br from-sky-500/10 to-blue-500/10 border border-sky-400/20 rounded-xl px-6 py-6">
              <div className="text-3xl font-bold text-sky-400 mb-2">5,000+</div>
              <div className="text-gray-300 text-sm">Active Traders</div>
            </div>
            <div className="bg-gradient-to-br from-sky-500/10 to-blue-500/10 border border-sky-400/20 rounded-xl px-6 py-6">
              <div className="text-3xl font-bold text-sky-400 mb-2">92%</div>
              <div className="text-gray-300 text-sm">Success Rate</div>
            </div>
            <div className="bg-gradient-to-br from-sky-500/10 to-blue-500/10 border border-sky-400/20 rounded-xl px-6 py-6">
              <div className="text-3xl font-bold text-sky-400 mb-2">48h</div>
              <div className="text-gray-300 text-sm">Avg. Payout Time</div>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  )
}
