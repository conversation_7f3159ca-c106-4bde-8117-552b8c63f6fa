"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { Eye, EyeOff, Lock, Mail, User, Phone, MapPin, Globe as GlobeIcon, Shield, ArrowRight } from "lucide-react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { signup } from "@/lib/api"
import { toast } from "@/hooks/use-toast"
import Image from "next/image"
import { trackCompleteRegistration } from "@/lib/meta-pixel"

export default function SignupPage() {
  const router = useRouter()
  const [showPassword, setShowPassword] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState("")
  const [step, setStep] = useState(1)
  const [formData, setFormData] = useState({
    username: "",
    email: "",
    password: "",
    name: "",
    phone_no: "",
    country: "",
    address: "",
    referral_code: "",
    agreeToTerms: false
  })

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData({
      ...formData,
      [field]: value
    })
    setError("") // Clear error when user starts typing
  }

  const handleSignUp = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setError("")

    try {
      // Validate required fields
      if (!formData.username || !formData.email || !formData.password || !formData.name ||
          !formData.phone_no || !formData.country || !formData.address) {
        throw new Error("Please fill in all required fields")
      }

      if (!formData.agreeToTerms) {
        throw new Error("Please agree to the terms and conditions")
      }

      const signupData = {
        username: formData.username,
        email: formData.email,
        password: formData.password,
        name: formData.name,
        phone_no: formData.phone_no,
        country: formData.country,
        address: formData.address,
        referral_code: formData.referral_code || undefined
      }

      await signup(signupData)

      // Track successful registration with Meta Pixel
      trackCompleteRegistration()

      toast({
        title: "Signup Successful",
        description: "Your account has been created! Please log in to continue.",
        duration: 3000,
      })

      router.push("/auth/login")
    } catch (error) {
      setError(error instanceof Error ? error.message : "Signup failed")
      toast({
        title: "Signup Failed",
        description: error instanceof Error ? error.message : "An error occurred during signup",
        variant: "destructive",
        duration: 5000,
      })
    } finally {
      setIsLoading(false)
    }
  }

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.5,
        staggerChildren: 0.1
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.5 }
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#001a2c] via-[#001e30] to-[#002235] flex items-center justify-center p-4">
      {/* Background elements */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-16 w-80 h-80 bg-sky-500/5 rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 right-16 w-96 h-96 bg-blue-500/5 rounded-full blur-3xl"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] bg-gradient-radial from-sky-500/3 to-transparent rounded-full"></div>
      </div>

      {/* Main Form */}
      <motion.div
        className="w-full max-w-md mx-auto z-10"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        <Card className="bg-white/10 backdrop-blur-lg border-2 border-sky-500/30 shadow-2xl ring-2 ring-sky-400/10 hover:ring-sky-400/30 transition-all duration-300 overflow-hidden">
          <CardHeader className="space-y-4 pb-6">
            <motion.div variants={itemVariants} className="text-center">
              <div className="flex items-center justify-center space-x-3 mb-4">
                <Image
                  src="https://res.cloudinary.com/dufcjjaav/image/upload/v1751695664/logo-removebg-preview_eqaddm.png"
                  alt="Funded Whales Logo"
                  width={64}
                  height={64}
                  className="w-16 h-16 drop-shadow-lg ring-2 ring-sky-400/30 rounded-2xl"
                />
                <div>
                  <CardTitle className="text-3xl font-extrabold text-white tracking-tight drop-shadow-lg">Create Account</CardTitle>
                  <CardDescription className="text-sky-200 text-lg font-medium">Join thousands of traders on FUNDEDWHALES</CardDescription>
                  <p className="text-sky-400 text-sm mt-2 font-semibold">Fast, secure, and easy account creation</p>
                </div>
              </div>
              {/* Step indicator */}
              <div className="flex items-center justify-center space-x-2 mt-4">
                <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium transition-all duration-300 ${
                  step >= 1 ? 'bg-gradient-to-r from-sky-400 to-blue-400 text-white shadow-lg' : 'bg-gray-600 text-gray-400'
                }`}>
                  1
                </div>
                <div className={`w-8 h-1 transition-all duration-300 ${step >= 2 ? 'bg-gradient-to-r from-sky-400 to-blue-400 shadow-lg' : 'bg-gray-600'}`}></div>
                <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium transition-all duration-300 ${
                  step >= 2 ? 'bg-gradient-to-r from-sky-400 to-blue-400 text-white shadow-lg' : 'bg-gray-600 text-gray-400'
                }`}>
                  2
                </div>
              </div>
              <p className="text-sm text-gray-400 mt-2">
                Step {step} of 2: {step === 1 ? 'Account Details' : 'Personal Information'}
              </p>
            </motion.div>
          </CardHeader>
          <CardContent>
            {error && (
              <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                className="mb-4 p-3 bg-red-500/10 border border-red-500/20 rounded-lg"
              >
                <p className="text-red-400 text-sm">{error}</p>
              </motion.div>
            )}

            <motion.div variants={containerVariants}>
              <form onSubmit={handleSignUp}>
                {step === 1 ? (
                  <div className="space-y-4">
                    <motion.div variants={itemVariants} className="space-y-2">
                      <Label htmlFor="username" className="text-white">Username *</Label>
                      <div className="relative">
                        <User className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                        <Input
                          id="username"
                          value={formData.username}
                          onChange={(e) => handleInputChange("username", e.target.value)}
                          placeholder="Enter your username"
                          className="pl-10 bg-[#001a2c] border-[#004c66] text-white h-12"
                          required
                        />
                      </div>
                    </motion.div>

                    <motion.div variants={itemVariants} className="space-y-2">
                      <Label htmlFor="name" className="text-white">Full Name *</Label>
                      <div className="relative">
                        <User className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                        <Input
                          id="name"
                          value={formData.name}
                          onChange={(e) => handleInputChange("name", e.target.value)}
                          placeholder="Enter your full name"
                          className="pl-10 bg-[#001a2c] border-[#004c66] text-white h-12"
                          required
                        />
                      </div>
                    </motion.div>

                    <motion.div variants={itemVariants} className="space-y-2">
                      <Label htmlFor="email" className="text-white">Email *</Label>
                      <div className="relative">
                        <Mail className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                        <Input
                          id="email"
                          type="email"
                          value={formData.email}
                          onChange={(e) => handleInputChange("email", e.target.value)}
                          placeholder="Enter your email"
                          className="pl-10 bg-[#001a2c] border-[#004c66] text-white h-12"
                          required
                        />
                      </div>
                    </motion.div>

                    <motion.div variants={itemVariants} className="space-y-2">
                      <Label htmlFor="password" className="text-white">Password *</Label>
                      <div className="relative">
                        <Lock className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                        <Input
                          id="password"
                          type={showPassword ? "text" : "password"}
                          value={formData.password}
                          onChange={(e) => handleInputChange("password", e.target.value)}
                          placeholder="Create a strong password"
                          className="pl-10 pr-10 bg-[#001a2c] border-[#004c66] text-white h-12"
                          required
                        />
                        <Button
                          type="button"
                          variant="ghost"
                          size="icon"
                          className="absolute right-0 top-0 h-12 w-12 text-gray-400 hover:text-white"
                          onClick={() => setShowPassword(!showPassword)}
                        >
                          {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                        </Button>
                      </div>
                    </motion.div>

                    <motion.div variants={itemVariants}>
                      <Button
                        type="button"
                        onClick={() => setStep(2)}
                        className="w-full bg-gradient-to-r from-sky-500 to-blue-600 hover:from-sky-600 hover:to-blue-700 text-white h-12 rounded-xl font-medium"
                      >
                        Continue
                        <ArrowRight className="ml-2 h-4 w-4" />
                      </Button>
                    </motion.div>
                  </div>
                ) : (
                  <div className="space-y-4">
                    <motion.div variants={itemVariants} className="space-y-2">
                      <Label htmlFor="phone_no" className="text-white">Phone Number *</Label>
                      <div className="relative">
                        <Phone className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                        <Input
                          id="phone_no"
                          value={formData.phone_no}
                          onChange={(e) => handleInputChange("phone_no", e.target.value)}
                          placeholder="Enter your phone number"
                          className="pl-10 bg-[#001a2c] border-[#004c66] text-white h-12"
                          required
                        />
                      </div>
                    </motion.div>

                    <motion.div variants={itemVariants} className="space-y-2">
                      <Label htmlFor="country" className="text-white">Country *</Label>
                      <div className="relative">
                        <GlobeIcon className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                        <Input
                          id="country"
                          value={formData.country}
                          onChange={(e) => handleInputChange("country", e.target.value)}
                          placeholder="Enter your country"
                          className="pl-10 bg-[#001a2c] border-[#004c66] text-white h-12"
                          required
                        />
                      </div>
                    </motion.div>

                    <motion.div variants={itemVariants} className="space-y-2">
                      <Label htmlFor="address" className="text-white">Address *</Label>
                      <div className="relative">
                        <MapPin className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                        <Input
                          id="address"
                          value={formData.address}
                          onChange={(e) => handleInputChange("address", e.target.value)}
                          placeholder="Enter your address"
                          className="pl-10 bg-[#001a2c] border-[#004c66] text-white h-12"
                          required
                        />
                      </div>
                    </motion.div>

                    <motion.div variants={itemVariants} className="space-y-2">
                      <Label htmlFor="referral_code" className="text-white">Referral Code (Optional)</Label>
                      <div className="relative">
                        <User className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                        <Input
                          id="referral_code"
                          value={formData.referral_code}
                          onChange={(e) => handleInputChange("referral_code", e.target.value)}
                          placeholder="Enter referral code (optional)"
                          className="pl-10 bg-[#001a2c] border-[#004c66] text-white h-12"
                        />
                      </div>
                    </motion.div>

                    <motion.div variants={itemVariants} className="space-y-4">
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="terms"
                          checked={formData.agreeToTerms}
                          onCheckedChange={(checked) => handleInputChange("agreeToTerms", checked)}
                          className="border-gray-400 data-[state=checked]:bg-sky-500 data-[state=checked]:border-sky-500"
                        />
                        <Label htmlFor="terms" className="text-sm text-gray-300">
                          I agree to the{" "}
                          <Link href="/terms" className="text-sky-400 hover:text-sky-300 underline">
                            Terms of Service
                          </Link>{" "}
                          and{" "}
                          <Link href="/privacy" className="text-sky-400 hover:text-sky-300 underline">
                            Privacy Policy
                          </Link>
                        </Label>
                      </div>
                    </motion.div>

                    <motion.div variants={itemVariants} className="space-y-3">
                      <Button
                        type="submit"
                        disabled={!formData.agreeToTerms || isLoading}
                        className="w-full bg-gradient-to-r from-sky-500 to-blue-600 hover:from-sky-600 hover:to-blue-700 text-white h-12 rounded-xl font-medium disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        {isLoading ? (
                          <motion.div
                            className="h-5 w-5 border-2 border-white border-t-transparent rounded-full"
                            animate={{ rotate: 360 }}
                            transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                          />
                        ) : (
                          <>
                            Create Account
                            <ArrowRight className="ml-2 h-4 w-4" />
                          </>
                        )}
                      </Button>
                      <Button
                        type="button"
                        variant="ghost"
                        onClick={() => setStep(1)}
                        className="w-full text-gray-400 hover:text-white"
                      >
                        Back to previous step
                      </Button>
                    </motion.div>
                  </div>
                )}
              </form>
            </motion.div>
          </CardContent>
          <div className="px-6 pb-6">
            <p className="text-center text-gray-400 text-sm">
              <span className="inline-flex items-center gap-2 justify-center text-sky-400 font-semibold"><Shield className="h-4 w-4" /> Trusted by 10,000+ traders</span>
              <br />
              Already have an account?{" "}
              <Link href="/auth/login" className="text-sky-400 hover:text-sky-300 font-medium transition-colors">
                Sign In
              </Link>
            </p>
          </div>
        </Card>
      </motion.div>
    </div>
  )
}
