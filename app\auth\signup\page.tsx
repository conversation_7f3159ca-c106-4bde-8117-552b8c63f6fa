"use client"

import { useState, useEffect } from "react"
import { motion } from "framer-motion"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Eye, EyeOff, Lock, Mail, User, Shield, ArrowRight, Github, CheckCircle, TrendingUp, Users, DollarSign, BarChart3, Globe, Zap, Target, Award, Clock, Phone, MapPin, Globe as GlobeIcon } from "lucide-react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { signup } from "@/lib/api"
import { toast } from "@/hooks/use-toast"
import { trackCompleteRegistration } from "@/lib/meta-pixel"

export default function SignUpPage() {
  const router = useRouter()
  const [showPassword, setShowPassword] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState("")
  const [step, setStep] = useState(1)
  const [formData, setFormData] = useState({
    username: "",
    email: "",
    password: "",
    name: "",
    phone_no: "",
    country: "",
    address: "",
    referral_code: "",
    agreeToTerms: false
  })

  const handleInputChange = (field: string, value: string) => {
    setFormData({
      ...formData,
      [field]: value
    })
    setError("") // Clear error when user starts typing
  }

  const handleSignUp = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setError("")

    try {
      // Validate required fields
      if (!formData.username || !formData.email || !formData.password || !formData.name || 
          !formData.phone_no || !formData.country || !formData.address) {
        throw new Error("Please fill in all required fields")
      }

      const signupData = {
        username: formData.username,
        email: formData.email,
        password: formData.password,
        name: formData.name,
        phone_no: formData.phone_no,
        country: formData.country,
        address: formData.address,
        referral_code: formData.referral_code || undefined
      }

      await signup(signupData)

      // Track successful registration with Meta Pixel
      trackCompleteRegistration()

      toast({
        title: "Signup Successful",
        description: "Your account has been created! Please log in to continue.",
        duration: 3000,
      })
      setTimeout(() => {
        router.push("/auth/login")
      }, 1200)
    } catch (error) {
      setError(error instanceof Error ? error.message : "Signup failed. Please try again.")
    } finally {
      setIsLoading(false)
    }
  }

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.5,
        staggerChildren: 0.1
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.5 }
    }
  }

  const slideVariants = {
    enter: (direction: number) => ({
      x: direction > 0 ? 1000 : -1000,
      opacity: 0
    }),
    center: {
      zIndex: 1,
      x: 0,
      opacity: 1
    },
    exit: (direction: number) => ({
      zIndex: 0,
      x: direction < 0 ? 1000 : -1000,
      opacity: 0
    })
  }

  const accountTypes = [
    { value: "individual", label: "Individual Trader" },
    { value: "professional", label: "Professional Trader" },
    { value: "institutional", label: "Institutional" }
  ]

  return (
    <div className="min-h-screen flex items-center justify-center p-4 bg-gradient-to-br from-[#001a2c] to-[#000c14] relative overflow-hidden">
      {/* Background Trading Elements */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-20 left-10 w-32 h-32 bg-sky-500/20 rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 right-10 w-40 h-40 bg-blue-500/20 rounded-full blur-3xl"></div>
        <div className="absolute top-1/2 left-1/4 w-24 h-24 bg-purple-500/20 rounded-full blur-2xl"></div>
        <div className="absolute top-1/3 right-1/4 w-20 h-20 bg-green-500/20 rounded-full blur-2xl"></div>
      </div>

      {/* Animated Background Bubbles */}
      <div className="absolute inset-0 overflow-hidden">
        {Array.from({ length: 20 }).map((_, i) => (
        <motion.div
            key={i}
            className="absolute rounded-full bg-sky-500/10"
            initial={{
              width: Math.random() * 100 + 20,
              height: Math.random() * 100 + 20,
              x: Math.random() * (typeof window !== 'undefined' ? window.innerWidth : 1200),
              y: (typeof window !== 'undefined' ? window.innerHeight : 800) + 100,
              opacity: Math.random() * 0.5 + 0.1,
            }}
            animate={{
              y: -200,
              opacity: 0,
            }}
            transition={{
              duration: Math.random() * 10 + 15,
              repeat: Number.POSITIVE_INFINITY,
              delay: Math.random() * 5,
            }}
          />
        ))}
              </div>

      {/* Centered Form Only */}
        <motion.div
        className="w-full max-w-md mx-auto z-10"
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          <Card className="bg-white/10 backdrop-blur-lg border-2 border-sky-500/30 shadow-2xl ring-2 ring-sky-400/10 hover:ring-sky-400/30 transition-all duration-300 overflow-hidden">
            <CardHeader className="space-y-4 pb-6">
              <motion.div variants={itemVariants}>
                <div className="w-16 h-16 rounded-2xl bg-gradient-to-br from-sky-400 to-blue-600 flex items-center justify-center mb-4 mx-auto shadow-lg ring-2 ring-sky-400/30">
                  <Shield className="h-8 w-8 text-white" />
                </div>
                <CardTitle className="text-3xl font-extrabold text-center text-white tracking-tight drop-shadow-lg">Create Account</CardTitle>
                <CardDescription className="text-center text-sky-200 text-lg font-medium">
                  Join our trading platform in a few steps
                </CardDescription>
                <p className="text-sky-400 text-sm mt-2 text-center font-semibold">Fast, secure, and easy account creation</p>
              </motion.div>
              <div className="flex justify-center space-x-2 pt-4">
                {[1, 2].map((i) => (
                  <motion.div
                    key={i}
                    className={`h-2 rounded-full transition-all duration-300 ${
                      i === step ? "w-8 bg-gradient-to-r from-sky-400 to-blue-400 shadow-lg" : "w-2 bg-[#004c66]"
                    }`}
                    animate={{ backgroundColor: i === step ? "#0ea5e9" : "#004c66" }}
                    transition={{ duration: 0.3 }}
                  />
                ))}
              </div>
            </CardHeader>
            <CardContent className="space-y-6">
              <motion.div
                key={step}
                custom={step}
                variants={slideVariants}
                initial="enter"
                animate="center"
                exit="exit"
                transition={{
                  x: { type: "spring", stiffness: 300, damping: 30 },
                  opacity: { duration: 0.2 }
                }}
              >
                {error && (
                  <motion.div variants={itemVariants} className="bg-red-500/10 border border-red-500/20 rounded-lg p-3">
                    <p className="text-red-400 text-sm">{error}</p>
                  </motion.div>
                )}

                {step === 1 ? (
                  <div className="space-y-4">
                    <motion.div variants={itemVariants} className="space-y-2">
                      <Label htmlFor="username" className="text-white">Username</Label>
                      <div className="relative">
                        <User className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                        <Input
                          id="username"
                          value={formData.username}
                          onChange={(e) => handleInputChange("username", e.target.value)}
                          placeholder="Enter your username"
                          className="pl-10 bg-[#001a2c] border-[#004c66] text-white h-12"
                          required
                        />
                      </div>
                    </motion.div>

                    <motion.div variants={itemVariants} className="space-y-2">
                      <Label htmlFor="name" className="text-white">Full Name</Label>
                      <div className="relative">
                        <User className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                        <Input
                          id="name"
                          value={formData.name}
                          onChange={(e) => handleInputChange("name", e.target.value)}
                          placeholder="Enter your full name"
                          className="pl-10 bg-[#001a2c] border-[#004c66] text-white h-12"
                          required
                        />
                      </div>
                    </motion.div>

                    <motion.div variants={itemVariants} className="space-y-2">
                      <Label htmlFor="email" className="text-white">Email</Label>
                      <div className="relative">
                        <Mail className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                        <Input
                          id="email"
                          type="email"
                          value={formData.email}
                          onChange={(e) => handleInputChange("email", e.target.value)}
                          placeholder="Enter your email"
                          className="pl-10 bg-[#001a2c] border-[#004c66] text-white h-12"
                          required
                        />
                      </div>
                    </motion.div>

                    <motion.div variants={itemVariants} className="space-y-2">
                      <Label htmlFor="password" className="text-white">Password</Label>
                      <div className="relative">
                        <Lock className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                        <Input
                          id="password"
                          type={showPassword ? "text" : "password"}
                          value={formData.password}
                          onChange={(e) => handleInputChange("password", e.target.value)}
                          placeholder="Create a strong password"
                          className="pl-10 pr-10 bg-[#001a2c] border-[#004c66] text-white h-12"
                          required
                        />
                        <Button
                          type="button"
                          variant="ghost"
                          size="icon"
                          className="absolute right-0 top-0 h-12 w-12 text-gray-400 hover:text-white"
                          onClick={() => setShowPassword(!showPassword)}
                        >
                          {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                        </Button>
                      </div>
                    </motion.div>

                    <motion.div variants={itemVariants}>
                      <Button
                        onClick={() => setStep(2)}
                        className="w-full bg-gradient-to-r from-sky-500 to-blue-600 hover:from-sky-600 hover:to-blue-700 text-white h-12 rounded-xl font-medium"
                      >
                        Continue
                        <ArrowRight className="ml-2 h-4 w-4" />
                      </Button>
                    </motion.div>
                  </div>
                ) : (
                  <div className="space-y-4">
                    <motion.div variants={itemVariants} className="space-y-2">
                      <Label htmlFor="phone_no" className="text-white">Phone Number</Label>
                      <div className="relative">
                        <Phone className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                        <Input
                          id="phone_no"
                          value={formData.phone_no}
                          onChange={(e) => handleInputChange("phone_no", e.target.value)}
                          placeholder="Enter your phone number"
                          className="pl-10 bg-[#001a2c] border-[#004c66] text-white h-12"
                          required
                        />
                      </div>
                    </motion.div>

                    <motion.div variants={itemVariants} className="space-y-2">
                      <Label htmlFor="country" className="text-white">Country</Label>
                      <div className="relative">
                        <GlobeIcon className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                        <Input
                          id="country"
                          value={formData.country}
                          onChange={(e) => handleInputChange("country", e.target.value)}
                          placeholder="Enter your country"
                          className="pl-10 bg-[#001a2c] border-[#004c66] text-white h-12"
                          required
                        />
                      </div>
                    </motion.div>

                    <motion.div variants={itemVariants} className="space-y-2">
                      <Label htmlFor="address" className="text-white">Address</Label>
                      <div className="relative">
                        <MapPin className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                        <Input
                          id="address"
                          value={formData.address}
                          onChange={(e) => handleInputChange("address", e.target.value)}
                          placeholder="Enter your address"
                          className="pl-10 bg-[#001a2c] border-[#004c66] text-white h-12"
                          required
                        />
                      </div>
                    </motion.div>

                    <motion.div variants={itemVariants} className="space-y-2">
                      <Label htmlFor="referral_code" className="text-white">Referral Code (Optional)</Label>
                      <div className="relative">
                        <User className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                        <Input
                          id="referral_code"
                          value={formData.referral_code}
                          onChange={(e) => handleInputChange("referral_code", e.target.value)}
                          placeholder="Enter referral code (optional)"
                          className="pl-10 bg-[#001a2c] border-[#004c66] text-white h-12"
                        />
                      </div>
                    </motion.div>

                    <motion.div variants={itemVariants} className="space-y-4">
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="terms"
                          checked={formData.agreeToTerms}
                          onCheckedChange={(checked) =>
                            setFormData({ ...formData, agreeToTerms: checked as boolean })
                          }
                          className="border-[#004c66] data-[state=checked]:bg-sky-500"
                        />
                        <label
                          htmlFor="terms"
                          className="text-sm text-gray-400"
                        >
                          I agree to the{" "}
                          <Link href="/terms" className="text-sky-400 hover:text-sky-300">
                            Terms of Service
                          </Link>{" "}
                          and{" "}
                          <Link href="/privacy" className="text-sky-400 hover:text-sky-300">
                            Privacy Policy
                          </Link>
                        </label>
                      </div>
                    </motion.div>

                    <motion.div variants={itemVariants} className="space-y-3">
                      <Button
                        onClick={handleSignUp}
                        disabled={!formData.agreeToTerms || isLoading}
                        className="w-full bg-gradient-to-r from-sky-500 to-blue-600 hover:from-sky-600 hover:to-blue-700 text-white h-12 rounded-xl font-medium"
                      >
                        {isLoading ? (
                          <motion.div
                            className="h-5 w-5 border-2 border-white border-t-transparent rounded-full"
                            animate={{ rotate: 360 }}
                            transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                          />
                        ) : (
                          <>
                            Create Account
                            <ArrowRight className="ml-2 h-4 w-4" />
                          </>
                        )}
                      </Button>
                      <Button
                        type="button"
                        variant="ghost"
                        onClick={() => setStep(1)}
                        className="w-full text-gray-400 hover:text-white"
                      >
                        Back to previous step
                      </Button>
                    </motion.div>
                  </div>
                )}
              </motion.div>
            </CardContent>
            <CardFooter>
              <motion.p variants={itemVariants} className="text-center text-gray-400 text-sm w-full">
                <span className="inline-flex items-center gap-2 justify-center text-sky-400 font-semibold"><Shield className="h-4 w-4" /> Trusted by 10,000+ traders</span>
                <br />
                Already have an account?{" "}
                <Link href="/auth/login" className="text-sky-400 hover:text-sky-300 font-medium transition-colors">
                  Sign In
                </Link>
              </motion.p>
            </CardFooter>
          </Card>
        </motion.div>
    </div>
  )
} 