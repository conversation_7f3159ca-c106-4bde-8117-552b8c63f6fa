"use client"

import { motion } from "framer-motion"
import { Facebook, Instagram, Linkedin, Twitter, Share2 } from "lucide-react"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>ipProvider, TooltipTrigger } from "@/components/ui/tooltip"

export default function SocialMedia() {
  const socialLinks = [
    {
      name: "Discord",
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
          className="w-6 h-6"
        >
          <path d="M9 12a1 1 0 1 0 2 0a1 1 0 1 0 -2 0" />
          <path d="M15 12a1 1 0 1 0 2 0a1 1 0 1 0 -2 0" />
          <path d="M8.5 17c0 1 -1.356 3 -1.832 3c-1.429 0 -2.698 -1.667 -3.333 -3c-.635 -1.667 -.476 -5.833 1.428 -11.5c1.388 -1.015 3.025 -1.34 4.5 -1.5l.238 2.5" />
          <path d="M14.5 17c0 1 1.5 3 2 3c1.5 0 2.764 -1.667 3.5 -3c.736 -1.333 .476 -5.833 -1.5 -11.5c-1.457 -1.015 -3.248 -1.34 -4.5 -1.5l-.25 2.5" />
          <path d="M7 16.5c3.5 1 6.5 1 10 0" />
        </svg>
      ),
      url: "https://discord.gg/hMWxAwegnc",
      description: "Join our trading community",
    },
    {
      name: "Facebook",
      icon: <Facebook className="w-6 h-6" />, 
      url: "https://www.facebook.com/share/1Ehxffb3rA/",
      description: "Stay updated with news",
    },
    {
      name: "Instagram",
      icon: <Instagram className="w-6 h-6" />, 
      url: "https://www.instagram.com/fundedwhales?igsh=MTYxb2QxdnppajdzMg==",
      description: "Follow for insights",
    },
    {
      name: "Telegram",
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
          className="w-6 h-6"
        >
          <path d="M11.944 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12 12 12 0 0 0 12-12A12 12 0 0 0 12 0a12 12 0 0 0-.056 0zm4.962 7.224c.1-.002.321.023.465.14a.506.506 0 0 1 .171.325c.016.093.036.306.02.472-.18 1.898-.96 6.502-1.36 8.627-.168.9-.499 1.201-.82 1.23-.696.065-1.225-.46-1.9-.902-1.056-.693-1.653-1.124-2.678-1.8-1.185-.78-.417-1.21.258-1.91.177-.184 3.247-2.977 3.307-3.23.007-.032.014-.15-.056-.212s-.174-.041-.249-.024c-.106.024-1.793 1.14-5.061 3.345-.48.33-.913.49-1.302.48-.428-.008-1.252-.241-1.865-.44-.752-.245-1.349-.374-1.297-.789.027-.216.325-.437.893-.663 3.498-1.524 5.83-2.529 6.998-3.014 3.332-1.386 4.025-1.627 4.476-1.635z" />
        </svg>
      ),
      url: "https://t.me/fundedwhales1",
      description: "Get instant updates",
    },
  ]

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 10 },
    visible: { opacity: 1, y: 0 },
    hover: {
      scale: 1.05,
      y: -5,
      transition: { duration: 0.3 },
    },
  }

  return (
    <section className="py-28 bg-gradient-to-b from-[#001a2c] via-[#001e30] to-[#002235] relative overflow-hidden">
      {/* Background elements */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-16 w-80 h-80 bg-sky-500/5 rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 right-16 w-96 h-96 bg-blue-500/5 rounded-full blur-3xl"></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <div className="flex items-center justify-center mb-4">
            <span className="text-sky-400 font-medium tracking-wider uppercase text-sm flex items-center gap-2">
              <Share2 className="h-4 w-4" />
              Connect With Us
            </span>
          </div>
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-4 text-white">
            Join Our{" "}
            <span className="bg-gradient-to-r from-sky-400 to-blue-400 bg-clip-text text-transparent">
              Trading Community
            </span>
          </h2>
          <p className="text-lg text-gray-300 max-w-3xl mx-auto leading-relaxed">
            Stay connected with our team and fellow traders. Get the latest market insights, trading tips, and exclusive updates across all our social platforms.
          </p>
        </motion.div>

        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="grid grid-cols-2 sm:grid-cols-4 gap-6 mx-auto justify-center"
        >
          {socialLinks.map((social, index) => (
            <motion.a
              key={index}
              variants={itemVariants}
              whileHover="hover"
              href={social.url}
              target="_blank"
              rel="noopener noreferrer"
              aria-label={`Follow us on ${social.name}`}
              className="group flex flex-col items-center justify-center p-6 rounded-2xl bg-gradient-to-br from-[#002a3c]/90 to-[#001e30]/90 border border-sky-500/20 hover:border-sky-400/40 transition-all duration-300 hover:scale-105 shadow-xl backdrop-blur-md"
            >
              <div className="flex items-center justify-center w-12 h-12 rounded-xl bg-gradient-to-br from-sky-500/20 to-blue-500/20 border border-sky-400/30 mb-4 group-hover:bg-sky-500/30 transition-all duration-300">
                {social.icon}
              </div>
              <span className="text-white font-semibold text-sm mb-1 group-hover:text-sky-300 transition-colors">
                {social.name}
              </span>
              <span className="text-xs text-gray-400 group-hover:text-sky-200 transition-colors text-center">
                {social.description}
              </span>
            </motion.a>
          ))}
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, delay: 0.6 }}
          className="text-center mt-12"
        >
          <div className="inline-flex items-center bg-gradient-to-r from-sky-500/10 to-blue-500/10 border border-sky-400/20 rounded-full px-6 py-3">
            <span className="text-sky-400 font-medium">Join thousands of traders in our global network</span>
          </div>
        </motion.div>
      </div>
    </section>
  )
}
