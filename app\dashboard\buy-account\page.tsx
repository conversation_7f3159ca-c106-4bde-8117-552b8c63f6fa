"use client"

import { useState, useEffect } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import {
  CheckCircle,
  Loader2,
  CreditCard,
  QrCode,
  Check,
  DollarSign,
  ChevronRight,
  ChevronLeft,
  AlertCircle,
  Info,
  Shield,
  Clock,
  Users,
  TrendingUp
} from "lucide-react"
import { createOrder, getToken, getUserProfile } from "@/lib/api"
import { useToast } from "@/hooks/use-toast"
import { useRouter } from "next/navigation"
import { trackPurchase, trackInitiateCheckout } from "@/lib/meta-pixel"

// Function to extract user email from JWT token
function getUserEmailFromToken(): string {
  try {
    const token = getToken()
    console.log('Token exists:', !!token)
    if (!token) {
      console.log('No token found in localStorage')
      return ""
    }

    // Decode JWT token payload
    const payload = JSON.parse(atob(token.split('.')[1]))

    // Debug: Log the payload to see what fields are available
    console.log('JWT Payload:', payload)
    console.log('Available fields:', Object.keys(payload))

    // Try different possible email fields in the JWT payload
    // Common JWT fields: email, sub (subject), username, user_email, mail
    const email = payload.email || payload.user_email || payload.mail || payload.sub || payload.username || ""
    console.log('Extracted email/identifier:', email)

    return email
  } catch (error) {
    console.error('Error extracting email from token:', error)
    return ""
  }
}

// Async function to get user email from multiple sources
async function getUserEmail(): Promise<string> {
  console.log('getUserEmail: Starting...')

  // First try to get email from JWT token
  const emailFromToken = getUserEmailFromToken()
  console.log('getUserEmail: Email from token:', emailFromToken)

  if (emailFromToken && emailFromToken.includes('@')) {
    console.log('getUserEmail: Found valid email in token:', emailFromToken)
    return emailFromToken
  }

  // If JWT doesn't contain email, try to get it from user profile API
  console.log('getUserEmail: Trying user profile API...')
  try {
    const userProfile = await getUserProfile()
    console.log('getUserEmail: User profile response:', userProfile)
    if (userProfile && userProfile.email) {
      console.log('getUserEmail: Found email in profile:', userProfile.email)
      return userProfile.email
    }
  } catch (error) {
    console.error('getUserEmail: Error fetching user profile:', error)
  }

  // Return the token value even if it doesn't look like an email (might be username)
  console.log('getUserEmail: Returning token value as fallback:', emailFromToken)
  return emailFromToken
}

export default function BuyAccountPage() {
  const { toast } = useToast()
  const router = useRouter()
  const [currentStep, setCurrentStep] = useState(1)
  const [formData, setFormData] = useState({
    email: "",
    challengeType: "",
    platform: "",
    size: "",
    paymentMethod: "",
    txid: "",
    proofImage: null as File | null,
  })

  // Add test function to global window for debugging (development only)
  if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
    (window as any).testEmailAutoFill = async () => {
      console.log('=== Testing Email Auto-fill ===')
      const token = getToken()
      console.log('Token exists:', !!token)
      if (token) {
        console.log('Token preview:', token.substring(0, 50) + '...')
        try {
          const payload = JSON.parse(atob(token.split('.')[1]))
          console.log('JWT Payload:', payload)
        } catch (e) {
          console.error('Error decoding token:', e)
        }
      }

      const email = await getUserEmail()
      console.log('Final email result:', email)
      console.log('=== End Test ===')
      return email
    }
  }

  const [orderPlaced, setOrderPlaced] = useState(false)
  const [selectedPrice, setSelectedPrice] = useState<number>(0)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [validationErrors, setValidationErrors] = useState<{[key: string]: string}>({})
  const [debugInfo, setDebugInfo] = useState<string>("")

  // Auto-fill email from logged-in user's token on component mount
  useEffect(() => {
    const loadUserEmail = async () => {
      try {
        console.log('Loading user email...')
        setDebugInfo('Loading user email...')

        const token = getToken()
        if (!token) {
          setDebugInfo('No token found - user not logged in')
          return
        }

        setDebugInfo('Token found, extracting email...')
        const userEmail = await getUserEmail()
        console.log('Retrieved user email:', userEmail)

        if (userEmail) {
          console.log('Setting email to:', userEmail)
          setFormData(prev => ({ ...prev, email: userEmail }))
          setDebugInfo(`Email auto-filled: ${userEmail}`)
        } else {
          console.log('No user email found')
          setDebugInfo('No email found in token or API')
        }
      } catch (error) {
        console.error('Error loading user email:', error)
        setDebugInfo(`Error: ${error}`)
      }
    }

    loadUserEmail()
  }, [])

  const challengeTypes = [
    {
      value: "instant",
      label: "Instant",
      description: "Instant Funding Challenge",
      price: 45,
      popular: true,
      features: ["No evaluation", "Immediate access", "Fastest payout"],
      color: "from-yellow-400 to-orange-500",
      icon: <TrendingUp className="h-5 w-5" />
    },
    { 
      value: "hft",
      label: "HFT",
      description: "High-Frequency Trading Challenge",
      price: 14,
      popular: false,
      features: ["Unlimited trading time", "Advanced algorithms", "API trading"],
      color: "from-purple-500 to-pink-500",
      icon: <Clock className="h-5 w-5" />
    },
    { 
      value: "one-step",
      label: "One-Step",
      description: "One-Step Evaluation Challenge",
      price: 8,
      popular: false,
      features: ["Simple rules", "Quick evaluation", "Low cost"],
      color: "from-blue-500 to-cyan-500",
      icon: <Check className="h-5 w-5" />
    },
    { 
      value: "two-step",
      label: "Two-Step",
      description: "Two-Step Evaluation Challenge",
      price: 6,
      popular: false,
      features: ["Standard evaluation", "Best for beginners"],
      color: "from-green-500 to-emerald-500",
      icon: <Users className="h-5 w-5" />
    },
  ]

  const platforms = [
    { value: "mt4", label: "MetaTrader 4" },
    { value: "mt5", label: "MetaTrader 5" },
  ]

  const sizes = [
    { value: "1000", label: "$1,000" },
    { value: "3000", label: "$3,000" },
    { value: "5000", label: "$5,000" },
    { value: "10000", label: "$10,000" },
    { value: "25000", label: "$25,000" },
    { value: "50000", label: "$50,000" },
    { value: "100000", label: "$100,000" },
    { value: "200000", label: "$200,000" },
    { value: "500000", label: "$500,000" },
  ];

  const paymentMethods = [
    { 
      value: "matic", 
      label: "Matic (Polygon)", 
      network: "Polygon",
      address: "******************************************",
      logo: "https://assets.coingecko.com/coins/images/4713/small/matic-token-icon.png",
      qrCode: `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=******************************************`
    },
    { 
      value: "usdt-trc20", 
      label: "USDT (TRC20)", 
      network: "TRON",
      address: "TTjfvRAzpb2Uz59kJR7ngRfPSjVGqL64ex",
      logo: "https://assets.coingecko.com/coins/images/325/small/Tether.png",
      qrCode: `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=TTjfvRAzpb2Uz59kJR7ngRfPSjVGqL64ex`
    },
    { 
      value: "usdt-bep20", 
      label: "USDT (BEP20)", 
      network: "BSC",
      address: "******************************************",
      logo: "https://assets.coingecko.com/coins/images/325/small/Tether.png",
      qrCode: `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=******************************************`
    },
    { 
      value: "bnb", 
      label: "BNB", 
      network: "BSC",
      address: "******************************************",
      logo: "https://assets.coingecko.com/coins/images/825/small/bnb-icon2_2x.png",
      qrCode: `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=******************************************`
    },
    { 
      value: "sol", 
      label: "Solana", 
      network: "Solana",
      address: "5AC4kgr3nG6QMhQy7fyCvsnCqmwAvVM1m1XApe1yBmGd",
      logo: "https://assets.coingecko.com/coins/images/4128/small/solana.png",
      qrCode: `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=5AC4kgr3nG6QMhQy7fyCvsnCqmwAvVM1m1XApe1yBmGd`
    },
    { 
      value: "eth", 
      label: "Ethereum", 
      network: "Ethereum",
      address: "******************************************",
      logo: "https://assets.coingecko.com/coins/images/279/small/ethereum.png",
      qrCode: `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=******************************************`
    },
    { 
      value: "btc", 
      label: "Bitcoin", 
      network: "Bitcoin",
      address: "******************************************",
      logo: "https://assets.coingecko.com/coins/images/1/small/bitcoin.png",
      qrCode: `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=******************************************`
    },
  ]

  const validateStep = (step: number) => {
    const errors: {[key: string]: string} = {}

    if (step === 1) {
      if (!formData.challengeType) {
        errors.challengeType = "Please select a challenge type"
      }
    }

    if (step === 2) {
      if (!formData.email) {
        errors.email = "Email is required"
      } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
        errors.email = "Please enter a valid email address"
      }

      if (!formData.platform) {
        errors.platform = "Please select a trading platform"
      }

      if (!formData.size) {
        errors.size = "Please select an account size"
      }
    }

    if (step === 3) {
      if (!formData.paymentMethod) {
        errors.paymentMethod = "Please select a payment method"
      }

      if (!formData.txid) {
        errors.txid = "Transaction ID is required"
      } else if (formData.txid.length < 10) {
        errors.txid = "Please enter a valid transaction ID"
      }

      if (!formData.proofImage) {
        errors.proofImage = "Payment proof is required"
      }
    }

    setValidationErrors(errors)
    return Object.keys(errors).length === 0
  }

  const validateForm = () => {
    return validateStep(1) && validateStep(2) && validateStep(3)
  }

  // Add priceTable for dynamic pricing per challenge type
  const priceTable: Record<string, { real: number[]; discount: number[] }> = {
    "instant": {
      real:      [56, 131, 231, 419, 883, 1625, 2188, 2875, 4988],
      discount:  [45, 105, 185, 335, 706, 650, 875, 1150, 1995],
    },
    "hft": {
      real:      [18, 35, 48, 80, 124, 238, 275, 463, 813],
      discount:  [14, 28, 38, 64, 99, 95, 110, 185, 325],
    },
    "one-step": {
      real:      [10, 16, 28, 48, 93, 168, 248, 363, 650],
      discount:  [8, 13, 22, 38, 74, 67, 99, 145, 260],
    },
    "two-step": {
      real:      [8, 14, 25, 33, 69, 125, 188, 313, 563],
      discount:  [6, 11, 20, 26, 55, 50, 75, 125, 225],
    },
  };
  const sizeKeys = ["1000", "3000", "5000", "10000", "25000", "50000", "100000", "200000", "500000"];

  // Add a 60% OFF sale for demonstration
  const saleActive = true;
  const isPopularSize = (sizeValue: string) => sizeValue === "50000" || sizeValue === "100000";

  // Step navigation functions
  const nextStep = () => {
    if (validateStep(currentStep)) {
      if (currentStep < 3) {
        setCurrentStep(currentStep + 1)
      }
    } else {
      toast({
        title: "Please complete all required fields",
        description: "Fill in all required information before proceeding to the next step.",
        variant: "destructive",
      })
    }
  }

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
    }
  }

  // Update handleInputChange to update prices when challengeType or size changes
  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))

    // Clear validation error when user starts typing
    if (validationErrors[field]) {
      setValidationErrors(prev => ({ ...prev, [field]: "" }))
    }

    if (field === "paymentMethod" && value) {
      // Track initiate checkout when user selects payment method
      trackInitiateCheckout(selectedPrice, 'USD')
    }
    if (field === "challengeType") {
      // Reset size and price when challenge type changes
      setFormData(prev => ({ ...prev, size: "" }))
      setSelectedPrice(0)
    }
    if (field === "size") {
      // Use dynamic priceTable if challengeType is selected
      const idx = sizeKeys.indexOf(value)
      const challengeType = formData.challengeType || "instant"
      const priceArr = priceTable[challengeType] || priceTable["instant"]
      setSelectedPrice(idx >= 0 ? priceArr.real[idx] : 0)
    }
  }

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      // Validate file type and size
      const allowedTypes = ['image/jpeg', 'image/png', 'image/jpg']
      const maxSize = 5 * 1024 * 1024 // 5MB
      
      if (!allowedTypes.includes(file.type)) {
        setValidationErrors(prev => ({ ...prev, proofImage: "Please upload a valid image file (JPEG, PNG)" }))
        return
      }
      
      if (file.size > maxSize) {
        setValidationErrors(prev => ({ ...prev, proofImage: "File size must be less than 5MB" }))
        return
      }
      
      setFormData(prev => ({ ...prev, proofImage: file }))
      setValidationErrors(prev => ({ ...prev, proofImage: "" }))
    }
  }

  const handlePlaceOrder = async () => {
    if (!validateForm()) {
      toast({
        title: "Validation Error",
        description: "Please fix the errors in the form",
        variant: "destructive",
      })
      return
    }

    try {
      setIsSubmitting(true)
      
      const orderData = {
        email: formData.email,
        challenge_type: formData.challengeType,
        account_size: formData.size,
        platform: formData.platform,
        payment_method: formData.paymentMethod,
        txid: formData.txid,
        image: formData.proofImage!,
      }

      await createOrder(orderData)

      // Track purchase event with Meta Pixel
      trackPurchase(selectedPrice, 'USD')

      toast({
        title: "Order Placed Successfully!",
        description: "Your order has been submitted and is being processed. You will receive an email confirmation shortly.",
        duration: 5000,
      })

      // Redirect to dashboard immediately after successful order
      router.push('/dashboard')
    } catch (error) {
      console.error('Failed to place order:', error)
      toast({
        title: "Order Failed",
        description: error instanceof Error ? error.message : "Failed to place order. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const selectedPaymentMethod = paymentMethods.find(pm => pm.value === formData.paymentMethod)
  const selectedChallengeType = challengeTypes.find(ct => ct.value === formData.challengeType)

  if (orderPlaced) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-[#002a3c] via-[#003a4c] to-[#001a2c] flex items-center justify-center p-4">
        <Card className="w-full max-w-md bg-gradient-to-br from-[#001a2c]/90 via-[#002a3c]/90 to-[#003a4c]/90 backdrop-blur-xl border border-sky-900/60">
          <CardContent className="text-center p-8">
            <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
            <h2 className="text-2xl font-bold text-white mb-4">
              Order Placed Successfully!
            </h2>
            <p className="text-gray-300 mb-6">
              Your order has been submitted and is being processed. You will receive an email confirmation shortly.
            </p>
            <Button
              onClick={() => {
                setOrderPlaced(false)
                setCurrentStep(1)
                setFormData({
                  email: "",
                  challengeType: "",
                  platform: "",
                  size: "",
                  paymentMethod: "",
                  txid: "",
                  proofImage: null,
                })
                setSelectedPrice(0)
                setValidationErrors({})
              }}
              className="w-full bg-gradient-to-r from-sky-500 to-blue-600 hover:from-sky-600 hover:to-blue-700 text-white"
            >
              Place Another Order
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  const steps = [
    { number: 1, title: "Select Challenge", description: "Choose your challenge type" },
    { number: 2, title: "Configure Account", description: "Set up your account details" },
    { number: 3, title: "Payment", description: "Complete your payment" }
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#002a3c] via-[#003a4c] to-[#001a2c] text-white">
      <div className="max-w-6xl mx-auto px-4 py-8">
        {/* Professional Header */}
        <div className="mb-12">
          <div className="text-center mb-10">
            <div className="inline-flex items-center gap-3 mb-6">
              <div className="w-12 h-12 bg-gradient-to-r from-sky-500 to-blue-600 rounded-xl flex items-center justify-center shadow-lg">
                <Shield className="h-6 w-6 text-white" />
              </div>
              <div>
                <h1 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-white via-sky-100 to-blue-100 bg-clip-text text-transparent">
                  Premium Account Purchase
                </h1>
                <p className="text-sky-300 text-sm font-medium mt-1">Secure • Fast • Professional</p>
              </div>
            </div>
            <p className="text-gray-300 text-lg max-w-2xl mx-auto leading-relaxed">
              Join thousands of successful traders with our premium funded accounts.
              Complete your purchase through our secure three-step process.
            </p>

            {/* Debug Info - Development Only */}
            {process.env.NODE_ENV === 'development' && (
              <div className="mt-4 p-3 bg-yellow-500/20 border border-yellow-500/40 rounded-lg max-w-2xl mx-auto">
                <p className="text-yellow-300 text-sm">
                  <strong>Debug:</strong> {debugInfo || 'Initializing...'}
                </p>
                <p className="text-yellow-300 text-xs mt-1">
                  Current email: "{formData.email}"
                </p>
                <p className="text-yellow-300 text-xs mt-1">
                  Token exists: {getToken() ? 'Yes' : 'No'}
                </p>
                <button
                  onClick={async () => {
                    const email = await getUserEmail()
                    setDebugInfo(`Manual test result: ${email}`)
                    if (email) {
                      setFormData(prev => ({ ...prev, email: email }))
                    }
                  }}
                  className="mt-2 px-3 py-1 bg-yellow-500 text-black rounded text-xs hover:bg-yellow-400"
                >
                  Test Email Auto-fill
                </button>
              </div>
            )}
          </div>

          {/* Enhanced Step Progress Indicator */}
          <div className="relative max-w-4xl mx-auto">
            <div className="flex justify-between items-center mb-8">
              {steps.map((step, i) => (
                <div key={step.number} className="flex flex-col items-center relative z-10">
                  <div className={`relative rounded-full w-16 h-16 flex items-center justify-center font-bold text-lg transition-all duration-500 shadow-xl ${
                    currentStep >= step.number
                      ? 'bg-gradient-to-r from-sky-500 via-blue-500 to-blue-600 text-white shadow-sky-500/30'
                      : currentStep === step.number - 1
                      ? 'bg-gradient-to-r from-gray-600 to-gray-700 text-white shadow-gray-500/20'
                      : 'bg-gradient-to-r from-gray-800 to-gray-900 text-gray-400 shadow-gray-800/20'
                  }`}>
                    {currentStep > step.number ? (
                      <Check className="h-7 w-7" />
                    ) : (
                      <span className="text-xl font-bold">{step.number}</span>
                    )}
                    {currentStep === step.number && (
                      <div className="absolute inset-0 rounded-full bg-gradient-to-r from-sky-400 to-blue-500 animate-pulse opacity-30"></div>
                    )}
                  </div>
                  <div className="mt-4 text-center max-w-[120px]">
                    <div className={`font-bold text-sm transition-colors duration-300 ${
                      currentStep >= step.number ? 'text-sky-300' : 'text-gray-400'
                    }`}>
                      {step.title}
                    </div>
                    <div className={`text-xs mt-1 transition-colors duration-300 ${
                      currentStep >= step.number ? 'text-gray-300' : 'text-gray-500'
                    }`}>
                      {step.description}
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Progress Line */}
            <div className="absolute top-8 left-0 right-0 h-1 bg-gray-800 rounded-full -z-10">
              <div
                className="h-full bg-gradient-to-r from-sky-500 to-blue-600 rounded-full transition-all duration-700 ease-out shadow-lg shadow-sky-500/30"
                style={{ width: `${((currentStep - 1) / (steps.length - 1)) * 100}%` }}
              ></div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <div className="lg:col-span-2">
            {/* Step 1: Professional Challenge Type Selection */}
            {currentStep === 1 && (
              <div className="space-y-8">
                <div className="text-center mb-8">
                  <h2 className="text-3xl font-bold text-white mb-3">Choose Your Trading Challenge</h2>
                  <p className="text-gray-300 text-lg max-w-2xl mx-auto">
                    Select the challenge type that aligns with your trading experience and goals.
                    Each option is designed for different trading strategies and skill levels.
                  </p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {challengeTypes.map((type) => (
                    <Card
                      key={type.value}
                      className={`relative cursor-pointer transition-all duration-500 transform hover:scale-[1.02] ${
                        type.value === formData.challengeType
                          ? 'bg-gradient-to-br from-sky-500/20 via-blue-500/20 to-blue-600/20 border-2 border-sky-400 shadow-2xl shadow-sky-500/20'
                          : 'bg-gradient-to-br from-[#001a2c]/80 via-[#002a3c]/80 to-[#003a4c]/80 border border-sky-900/40 hover:border-sky-500/60 hover:shadow-xl hover:shadow-sky-500/10'
                      }`}
                      onClick={() => handleInputChange("challengeType", type.value)}
                    >
                      {type.popular && (
                        <div className="absolute -top-3 -right-3 z-10">
                          <div className="bg-gradient-to-r from-yellow-400 to-orange-500 text-white text-xs font-bold px-3 py-1 rounded-full shadow-lg animate-pulse">
                            ⭐ MOST POPULAR
                          </div>
                        </div>
                      )}

                      <CardHeader className="pb-4">
                        <div className="flex items-center gap-4 mb-4">
                          <div className={`w-14 h-14 rounded-xl flex items-center justify-center text-2xl shadow-lg ${
                            type.value === formData.challengeType
                              ? 'bg-gradient-to-r from-sky-400 to-blue-500 text-white'
                              : 'bg-gradient-to-r from-gray-700 to-gray-800 text-gray-300'
                          }`}>
                            {type.icon}
                          </div>
                          <div className="flex-1">
                            <CardTitle className="text-2xl text-white mb-1">{type.label}</CardTitle>
                            <p className="text-gray-300 text-sm">{type.description}</p>
                          </div>
                          {type.value === formData.challengeType && (
                            <div className="w-6 h-6 bg-gradient-to-r from-sky-400 to-blue-500 rounded-full flex items-center justify-center">
                              <Check className="h-4 w-4 text-white" />
                            </div>
                          )}
                        </div>
                      </CardHeader>

                      <CardContent className="pt-0">
                        <div className="space-y-3">
                          <h4 className="font-semibold text-white text-sm mb-3 flex items-center gap-2">
                            <Shield className="h-4 w-4 text-sky-400" />
                            Key Features
                          </h4>
                          {type.features.map((feature, idx) => (
                            <div key={idx} className="flex items-center gap-3 text-sm">
                              <div className="w-5 h-5 bg-gradient-to-r from-green-400 to-green-500 rounded-full flex items-center justify-center flex-shrink-0">
                                <Check className="h-3 w-3 text-white" />
                              </div>
                              <span className="text-gray-300">{feature}</span>
                            </div>
                          ))}
                        </div>

                        <div className="mt-6 pt-4 border-t border-gray-700/50">
                          <div className="flex items-center justify-between">
                            <span className="text-sm text-gray-400">Starting from</span>
                            <span className="text-lg font-bold text-sky-300">${type.price}</span>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>

                {validationErrors.challengeType && (
                  <div className="bg-red-500/10 border border-red-500/30 rounded-xl p-4 flex items-center gap-3">
                    <AlertCircle className="h-5 w-5 text-red-400 flex-shrink-0" />
                    <p className="text-red-300 font-medium">{validationErrors.challengeType}</p>
                  </div>
                )}
              </div>
            )}

            {/* Step 2: Professional Account Configuration */}
            {currentStep === 2 && (
              <div className="space-y-8">
                <div className="text-center mb-8">
                  <h2 className="text-3xl font-bold text-white mb-3">Configure Your Account</h2>
                  <p className="text-gray-300 text-lg max-w-2xl mx-auto">
                    Provide your account details and select the perfect account size for your trading strategy.
                  </p>
                </div>

                {/* Account Details Section */}
                <Card className="bg-gradient-to-br from-[#001a2c]/80 via-[#002a3c]/80 to-[#003a4c]/80 border border-sky-900/40 shadow-xl">
                  <CardHeader>
                    <CardTitle className="text-xl text-white flex items-center gap-3">
                      <div className="w-8 h-8 bg-gradient-to-r from-sky-400 to-blue-500 rounded-lg flex items-center justify-center">
                        <Users className="h-5 w-5 text-white" />
                      </div>
                      Account Information
                    </CardTitle>
                    <p className="text-gray-300 text-sm">Enter your contact details and preferred trading platform</p>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-2">
                        <Label htmlFor="email" className="text-sm font-semibold text-white flex items-center gap-2">
                          <div className="w-2 h-2 bg-sky-400 rounded-full"></div>
                          Email Address
                        </Label>
                        <Input
                          id="email"
                          type="email"
                          value={formData.email}
                          onChange={(e) => handleInputChange("email", e.target.value)}
                          className={`h-12 bg-[#001a2c]/60 border-2 text-white placeholder-gray-400 focus:border-sky-400 focus:ring-2 focus:ring-sky-400/20 transition-all duration-300 ${
                            validationErrors.email ? 'border-red-500 focus:border-red-500 focus:ring-red-500/20' : 'border-sky-900/60'
                          }`}
                          placeholder="<EMAIL>"
                        />
                        {validationErrors.email && (
                          <div className="bg-red-500/10 border border-red-500/30 rounded-lg p-3 flex items-center gap-2">
                            <AlertCircle className="h-4 w-4 text-red-400 flex-shrink-0" />
                            <p className="text-red-300 text-sm">{validationErrors.email}</p>
                          </div>
                        )}
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="platform" className="text-sm font-semibold text-white flex items-center gap-2">
                          <div className="w-2 h-2 bg-sky-400 rounded-full"></div>
                          Trading Platform
                        </Label>
                        <Select value={formData.platform} onValueChange={(value) => handleInputChange("platform", value)}>
                          <SelectTrigger className={`h-12 bg-[#001a2c]/60 border-2 text-white focus:border-sky-400 focus:ring-2 focus:ring-sky-400/20 transition-all duration-300 ${
                            validationErrors.platform ? 'border-red-500 focus:border-red-500 focus:ring-red-500/20' : 'border-sky-900/60'
                          }`}>
                            <SelectValue placeholder="Choose your preferred platform" />
                          </SelectTrigger>
                          <SelectContent className="bg-gradient-to-br from-[#001a2c] via-[#002a3c] to-[#003a4c] border-sky-900/60">
                            {platforms.map((platform) => (
                              <SelectItem key={platform.value} value={platform.value} className="text-white hover:bg-sky-500/20 focus:bg-sky-500/20">
                                <div className="flex items-center gap-3">
                                  <div className="w-6 h-6 bg-gradient-to-r from-sky-400 to-blue-500 rounded flex items-center justify-center text-xs font-bold text-white">
                                    {platform.label.includes('4') ? '4' : '5'}
                                  </div>
                                  {platform.label}
                                </div>
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        {validationErrors.platform && (
                          <div className="bg-red-500/10 border border-red-500/30 rounded-lg p-3 flex items-center gap-2">
                            <AlertCircle className="h-4 w-4 text-red-400 flex-shrink-0" />
                            <p className="text-red-300 text-sm">{validationErrors.platform}</p>
                          </div>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Account Size Selection */}
                <Card className="bg-gradient-to-br from-[#001a2c]/80 via-[#002a3c]/80 to-[#003a4c]/80 border border-sky-900/40 shadow-xl">
                  <CardHeader>
                    <CardTitle className="text-xl text-white flex items-center gap-3">
                      <div className="w-8 h-8 bg-gradient-to-r from-sky-400 to-blue-500 rounded-lg flex items-center justify-center">
                        <DollarSign className="h-5 w-5 text-white" />
                      </div>
                      Choose Account Size
                    </CardTitle>
                    <p className="text-gray-300 text-sm">Select the account size that matches your trading capital and risk management strategy</p>
                  </CardHeader>
                  <CardContent>
                    {formData.challengeType ? (
                      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                        {sizes.map((size, idx) => {
                          const challengeType = formData.challengeType
                          const priceArr = priceTable[challengeType] || priceTable["instant"]
                          const realPrice = priceArr.real[idx]
                          const discountedPrice = priceArr.discount[idx]
                          const savings = realPrice - discountedPrice
                          const savingsPercent = Math.round((savings / realPrice) * 100)

                          return (
                            <Card
                              key={size.value}
                              className={`relative cursor-pointer transition-all duration-300 transform hover:scale-105 ${
                                formData.size === size.value
                                  ? 'bg-gradient-to-br from-sky-500/20 via-blue-500/20 to-blue-600/20 border-2 border-sky-400 shadow-lg shadow-sky-500/20'
                                  : 'bg-gradient-to-br from-[#001a2c]/60 to-[#002a3c]/60 border border-sky-900/40 hover:border-sky-500/60'
                              }`}
                              onClick={() => handleInputChange("size", size.value)}
                            >
                              {isPopularSize(size.value) && (
                                <div className="absolute -top-2 -right-2 z-10">
                                  <div className="bg-gradient-to-r from-yellow-400 to-orange-500 text-white text-xs font-bold px-2 py-1 rounded-full shadow-lg">
                                    BEST VALUE
                                  </div>
                                </div>
                              )}
                              {saleActive && (
                                <div className="absolute -top-2 -left-2 z-10">
                                  <div className="bg-gradient-to-r from-green-400 to-green-600 text-white text-xs font-bold px-2 py-1 rounded-full shadow-lg">
                                    {savingsPercent}% OFF
                                  </div>
                                </div>
                              )}

                              <CardContent className="p-4 text-center">
                                <div className="mb-3">
                                  <div className="text-2xl font-bold text-white mb-1">{size.label}</div>
                                  <div className="text-xs text-gray-400">Account Size</div>
                                </div>

                                <div className="space-y-1 mb-4">
                                  <div className="text-sm text-gray-400 line-through">${realPrice}</div>
                                  <div className="text-2xl font-bold text-sky-300">${discountedPrice}</div>
                                  <div className="text-xs text-green-400 font-medium">Save ${savings}</div>
                                </div>

                                {formData.size === size.value && (
                                  <div className="w-6 h-6 bg-gradient-to-r from-sky-400 to-blue-500 rounded-full flex items-center justify-center mx-auto">
                                    <Check className="h-4 w-4 text-white" />
                                  </div>
                                )}
                              </CardContent>
                            </Card>
                          )
                        })}
                      </div>
                    ) : (
                      <div className="text-center py-12">
                        <div className="w-16 h-16 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full flex items-center justify-center mx-auto mb-4">
                          <AlertCircle className="h-8 w-8 text-white" />
                        </div>
                        <h3 className="text-xl font-bold text-white mb-2">Challenge Type Required</h3>
                        <p className="text-gray-300 mb-4">Please go back and select a challenge type to view pricing options.</p>
                        <Button
                          onClick={() => setCurrentStep(1)}
                          className="bg-gradient-to-r from-sky-500 to-blue-600 hover:from-sky-600 hover:to-blue-700"
                        >
                          <ChevronLeft className="h-4 w-4 mr-2" />
                          Select Challenge Type
                        </Button>
                      </div>
                    )}

                    {validationErrors.size && (
                      <div className="bg-red-500/10 border border-red-500/30 rounded-xl p-4 flex items-center gap-3 mt-6">
                        <AlertCircle className="h-5 w-5 text-red-400 flex-shrink-0" />
                        <p className="text-red-300 font-medium">{validationErrors.size}</p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>
            )}

            {/* Step 3: Professional Payment */}
            {currentStep === 3 && (
              <div className="space-y-8">
                <div className="text-center mb-8">
                  <h2 className="text-3xl font-bold text-white mb-3">Secure Payment</h2>
                  <p className="text-gray-300 text-lg max-w-2xl mx-auto">
                    Complete your purchase using cryptocurrency. All transactions are secure and encrypted.
                  </p>
                </div>

                {/* Payment Method Selection */}
                <Card className="bg-gradient-to-br from-[#001a2c]/80 via-[#002a3c]/80 to-[#003a4c]/80 border border-sky-900/40 shadow-xl">
                  <CardHeader>
                    <CardTitle className="text-xl text-white flex items-center gap-3">
                      <div className="w-8 h-8 bg-gradient-to-r from-green-400 to-green-500 rounded-lg flex items-center justify-center">
                        <CreditCard className="h-5 w-5 text-white" />
                      </div>
                      Choose Payment Method
                    </CardTitle>
                    <p className="text-gray-300 text-sm">Select your preferred cryptocurrency for secure payment processing</p>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                      {paymentMethods.map((method) => (
                        <Card
                          key={method.value}
                          className={`relative cursor-pointer transition-all duration-300 transform hover:scale-105 ${
                            formData.paymentMethod === method.value
                              ? 'bg-gradient-to-br from-green-500/20 via-green-500/20 to-green-600/20 border-2 border-green-400 shadow-lg shadow-green-500/20'
                              : 'bg-gradient-to-br from-[#001a2c]/60 to-[#002a3c]/60 border border-sky-900/40 hover:border-green-400/60'
                          }`}
                          onClick={() => handleInputChange("paymentMethod", method.value)}
                        >
                          <CardContent className="p-4 text-center">
                            <div className="mb-4">
                              <div className="w-16 h-16 mx-auto mb-3 rounded-full bg-white/10 flex items-center justify-center">
                                <img src={method.logo} alt={method.label} className="w-10 h-10 rounded-full" />
                              </div>
                              <div className="font-bold text-white text-sm mb-1">{method.label}</div>
                              <Badge
                                variant="secondary"
                                className={`text-xs ${
                                  formData.paymentMethod === method.value
                                    ? 'bg-green-500/20 text-green-300 border-green-500/40'
                                    : 'bg-sky-500/20 text-sky-300 border-sky-500/40'
                                }`}
                              >
                                {method.network}
                              </Badge>
                            </div>

                            {formData.paymentMethod === method.value && (
                              <div className="w-6 h-6 bg-gradient-to-r from-green-400 to-green-500 rounded-full flex items-center justify-center mx-auto">
                                <Check className="h-4 w-4 text-white" />
                              </div>
                            )}
                          </CardContent>
                        </Card>
                      ))}
                    </div>

                    {validationErrors.paymentMethod && (
                      <div className="bg-red-500/10 border border-red-500/30 rounded-xl p-4 flex items-center gap-3 mt-6">
                        <AlertCircle className="h-5 w-5 text-red-400 flex-shrink-0" />
                        <p className="text-red-300 font-medium">{validationErrors.paymentMethod}</p>
                      </div>
                    )}
                  </CardContent>
                </Card>

                {/* Enhanced Payment Details */}
                {formData.paymentMethod && selectedPaymentMethod && (
                  <Card className="bg-gradient-to-br from-[#001a2c]/80 via-[#002a3c]/80 to-[#003a4c]/80 border border-sky-900/40 shadow-xl">
                    <CardHeader>
                      <CardTitle className="text-xl text-white flex items-center gap-3">
                        <div className="w-8 h-8 bg-gradient-to-r from-orange-400 to-orange-500 rounded-lg flex items-center justify-center">
                          <QrCode className="h-5 w-5 text-white" />
                        </div>
                        Payment Instructions
                      </CardTitle>
                      <p className="text-gray-300 text-sm">Follow these steps to complete your secure payment</p>
                    </CardHeader>
                    <CardContent className="space-y-8">
                      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                        {/* QR Code and Amount Section */}
                        <div className="space-y-6">
                          <div className="text-center">
                            <div className="inline-block p-6 bg-white rounded-2xl shadow-lg mb-6">
                              <img
                                src={selectedPaymentMethod.qrCode}
                                alt="Payment QR Code"
                                className="w-40 h-40"
                              />
                            </div>
                            <div className="bg-gradient-to-br from-[#001a2c]/60 to-[#002a3c]/60 rounded-xl p-6 border border-sky-900/40">
                              <div className="text-3xl font-bold text-white mb-2">
                                ${selectedPrice}
                              </div>
                              <div className="text-sm text-gray-400 mb-4">Total Amount</div>
                              <div className="flex items-center justify-center gap-2 text-green-400">
                                <Shield className="h-4 w-4" />
                                <span className="text-sm font-medium">Secure Payment</span>
                              </div>
                            </div>
                          </div>
                        </div>

                        {/* Payment Form Section */}
                        <div className="space-y-6">
                          <div className="space-y-2">
                            <Label className="text-sm font-semibold text-white flex items-center gap-2">
                              <div className="w-2 h-2 bg-orange-400 rounded-full"></div>
                              Wallet Address
                            </Label>
                            <div className="p-4 bg-gradient-to-br from-[#001a2c]/60 to-[#002a3c]/60 rounded-xl border border-sky-900/40">
                              <div className="flex items-center justify-between gap-3">
                                <span className="text-sm text-gray-300 font-mono break-all flex-1">
                                  {selectedPaymentMethod.address}
                                </span>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => {
                                    navigator.clipboard.writeText(selectedPaymentMethod.address)
                                    toast({
                                      title: "Address Copied!",
                                      description: "Wallet address copied to clipboard",
                                      duration: 2000,
                                    })
                                  }}
                                  className="text-sky-400 hover:text-sky-300 hover:bg-sky-500/10 px-3 py-2 rounded-lg transition-all duration-200"
                                >
                                  Copy
                                </Button>
                              </div>
                            </div>
                          </div>

                          <div className="space-y-2">
                            <Label className="text-sm font-semibold text-white flex items-center gap-2">
                              <div className="w-2 h-2 bg-orange-400 rounded-full"></div>
                              Transaction ID (TXID)
                            </Label>
                            <Input
                              value={formData.txid}
                              onChange={(e) => handleInputChange("txid", e.target.value)}
                              className={`h-12 bg-[#001a2c]/60 border-2 text-white placeholder-gray-400 focus:border-orange-400 focus:ring-2 focus:ring-orange-400/20 transition-all duration-300 ${
                                validationErrors.txid ? 'border-red-500 focus:border-red-500 focus:ring-red-500/20' : 'border-sky-900/60'
                              }`}
                              placeholder="Paste your transaction ID here"
                            />
                            {validationErrors.txid && (
                              <div className="bg-red-500/10 border border-red-500/30 rounded-lg p-3 flex items-center gap-2">
                                <AlertCircle className="h-4 w-4 text-red-400 flex-shrink-0" />
                                <p className="text-red-300 text-sm">{validationErrors.txid}</p>
                              </div>
                            )}
                          </div>

                          <div className="space-y-2">
                            <Label className="text-sm font-semibold text-white flex items-center gap-2">
                              <div className="w-2 h-2 bg-orange-400 rounded-full"></div>
                              Payment Screenshot
                            </Label>
                            <div className="relative">
                              <Input
                                type="file"
                                accept="image/*"
                                onChange={handleFileUpload}
                                className={`h-12 bg-[#001a2c]/60 border-2 text-white file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-medium file:bg-orange-500 file:text-white hover:file:bg-orange-600 transition-all duration-300 ${
                                  validationErrors.proofImage ? 'border-red-500 focus:border-red-500 focus:ring-red-500/20' : 'border-sky-900/60 focus:border-orange-400 focus:ring-2 focus:ring-orange-400/20'
                                }`}
                              />
                            </div>
                            {formData.proofImage && (
                              <div className="bg-green-500/10 border border-green-500/30 rounded-lg p-3 flex items-center gap-2">
                                <CheckCircle className="h-4 w-4 text-green-400 flex-shrink-0" />
                                <p className="text-green-300 text-sm font-medium">{formData.proofImage.name}</p>
                              </div>
                            )}
                            {validationErrors.proofImage && (
                              <div className="bg-red-500/10 border border-red-500/30 rounded-lg p-3 flex items-center gap-2">
                                <AlertCircle className="h-4 w-4 text-red-400 flex-shrink-0" />
                                <p className="text-red-300 text-sm">{validationErrors.proofImage}</p>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )}
              </div>
            )}
          </div>

          {/* Enhanced Professional Sidebar */}
          <div className="space-y-6">
            {/* Premium Order Summary */}
            <div className="sticky top-8">
              <Card className="bg-gradient-to-br from-[#001a2c]/90 via-[#002a3c]/90 to-[#003a4c]/90 border-2 border-yellow-400/60 shadow-2xl rounded-2xl backdrop-blur-xl">
                <CardHeader className="pb-4">
                  <CardTitle className="text-xl text-white flex items-center gap-3">
                    <div className="w-8 h-8 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-lg flex items-center justify-center">
                      <DollarSign className="h-5 w-5 text-white" />
                    </div>
                    Order Summary
                  </CardTitle>
                  <p className="text-gray-300 text-sm">Review your selection details</p>
                </CardHeader>
                <CardContent className="space-y-6">
                  {selectedChallengeType ? (
                    <div className="space-y-4">
                      {/* Challenge Type */}
                      <div className="p-4 bg-gradient-to-br from-[#001a2c]/60 to-[#002a3c]/60 rounded-xl border border-sky-900/40">
                        <div className="flex items-center gap-3 mb-3">
                          <div className="w-10 h-10 bg-gradient-to-r from-sky-400 to-blue-500 rounded-lg flex items-center justify-center text-lg">
                            {selectedChallengeType.icon}
                          </div>
                          <div className="flex-1">
                            <div className="font-bold text-white">{selectedChallengeType.label}</div>
                            <div className="text-xs text-gray-400">{selectedChallengeType.description}</div>
                          </div>
                        </div>
                      </div>

                      {/* Account Details */}
                      {(formData.size || formData.platform || formData.email) && (
                        <div className="p-4 bg-gradient-to-br from-[#001a2c]/60 to-[#002a3c]/60 rounded-xl border border-sky-900/40">
                          <h4 className="font-semibold text-white text-sm mb-3 flex items-center gap-2">
                            <Users className="h-4 w-4 text-sky-400" />
                            Account Details
                          </h4>
                          <div className="space-y-2 text-sm">
                            {formData.size && (
                              <div className="flex justify-between items-center">
                                <span className="text-gray-400">Size:</span>
                                <span className="text-sky-300 font-medium">
                                  {sizes.find(s => s.value === formData.size)?.label}
                                </span>
                              </div>
                            )}
                            {formData.platform && (
                              <div className="flex justify-between items-center">
                                <span className="text-gray-400">Platform:</span>
                                <span className="text-sky-300 font-medium">
                                  {platforms.find(p => p.value === formData.platform)?.label}
                                </span>
                              </div>
                            )}
                            {formData.email && (
                              <div className="flex justify-between items-center">
                                <span className="text-gray-400">Email:</span>
                                <span className="text-sky-300 font-medium text-xs">
                                  {formData.email.length > 20 ? `${formData.email.substring(0, 20)}...` : formData.email}
                                </span>
                              </div>
                            )}
                          </div>
                        </div>
                      )}

                      {/* Payment Method */}
                      {formData.paymentMethod && (
                        <div className="p-4 bg-gradient-to-br from-[#001a2c]/60 to-[#002a3c]/60 rounded-xl border border-sky-900/40">
                          <h4 className="font-semibold text-white text-sm mb-3 flex items-center gap-2">
                            <CreditCard className="h-4 w-4 text-green-400" />
                            Payment Method
                          </h4>
                          <div className="flex items-center gap-3">
                            <img
                              src={selectedPaymentMethod?.logo}
                              alt={selectedPaymentMethod?.label}
                              className="w-8 h-8 rounded-full"
                            />
                            <div>
                              <div className="text-white font-medium text-sm">{selectedPaymentMethod?.label}</div>
                              <div className="text-gray-400 text-xs">{selectedPaymentMethod?.network}</div>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="p-6 bg-gradient-to-br from-[#001a2c]/60 to-[#002a3c]/60 rounded-xl border border-sky-900/40 text-center">
                      <div className="w-12 h-12 bg-gradient-to-r from-gray-600 to-gray-700 rounded-full flex items-center justify-center mx-auto mb-3">
                        <DollarSign className="h-6 w-6 text-gray-400" />
                      </div>
                      <div className="text-gray-400 text-sm">Complete the steps to see your order summary</div>
                    </div>
                  )}

                  {/* Total Section */}
                  <div className="border-t border-sky-900/40 pt-4">
                    <div className="bg-gradient-to-r from-yellow-400/10 to-orange-500/10 rounded-xl p-4 border border-yellow-400/30">
                      <div className="flex justify-between items-center">
                        <span className="text-lg font-bold text-white">Total Amount</span>
                        <div className="text-right">
                          <div className="text-3xl font-bold text-yellow-300">
                            ${selectedPrice}
                          </div>
                          <div className="text-xs text-gray-400">USD</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Professional Navigation Buttons */}
            <div className="space-y-4 mt-8">
              {currentStep < 3 ? (
                <Button
                  onClick={nextStep}
                  className="w-full h-14 bg-gradient-to-r from-sky-500 via-blue-500 to-blue-600 hover:from-sky-600 hover:via-blue-600 hover:to-blue-700 text-white text-lg font-bold rounded-xl shadow-2xl hover:shadow-sky-500/25 hover:scale-[1.02] transition-all duration-300 relative overflow-hidden group"
                  size="lg"
                >
                  <span className="relative z-10 flex items-center justify-center gap-3">
                    <span>Continue to {steps[currentStep]?.title}</span>
                    <ChevronRight className="h-5 w-5 group-hover:translate-x-1 transition-transform duration-300" />
                  </span>
                  <div className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/10 to-white/0 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700"></div>
                </Button>
              ) : (
                <Button
                  onClick={handlePlaceOrder}
                  disabled={isSubmitting}
                  className="w-full h-14 bg-gradient-to-r from-yellow-400 via-orange-400 to-orange-500 hover:from-yellow-500 hover:via-orange-500 hover:to-orange-600 disabled:from-gray-600 disabled:via-gray-700 disabled:to-gray-800 text-white text-lg font-bold rounded-xl shadow-2xl hover:shadow-yellow-500/25 hover:scale-[1.02] transition-all duration-300 relative overflow-hidden group"
                  size="lg"
                >
                  <span className="relative z-10 flex items-center justify-center gap-3">
                    {isSubmitting ? (
                      <>
                        <Loader2 className="h-5 w-5 animate-spin" />
                        <span>Processing Order...</span>
                      </>
                    ) : (
                      <>
                        <Shield className="h-5 w-5" />
                        <span>Complete Purchase</span>
                      </>
                    )}
                  </span>
                  {!isSubmitting && (
                    <div className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/20 to-white/0 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700"></div>
                  )}
                </Button>
              )}

              {currentStep > 1 && (
                <Button
                  onClick={prevStep}
                  variant="outline"
                  className="w-full h-12 border-2 border-sky-500/60 text-sky-400 hover:bg-sky-500/10 hover:border-sky-400 hover:text-sky-300 rounded-xl transition-all duration-300 group"
                  size="lg"
                >
                  <ChevronLeft className="h-5 w-5 mr-2 group-hover:-translate-x-1 transition-transform duration-300" />
                  Back to {steps[currentStep - 2]?.title}
                </Button>
              )}

              {/* Trust Indicators */}
              <div className="flex items-center justify-center gap-6 mt-6 pt-4 border-t border-sky-900/40">
                <div className="flex items-center gap-2">
                  <Shield className="h-4 w-4 text-green-400" />
                  <span className="text-green-300 text-xs font-medium">SSL Encrypted</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-blue-400" />
                  <span className="text-blue-300 text-xs font-medium">Verified Platform</span>
                </div>
                <div className="flex items-center gap-2">
                  <Users className="h-4 w-4 text-purple-400" />
                  <span className="text-purple-300 text-xs font-medium">10k+ Traders</span>
                </div>
              </div>
            </div>

            {/* Enhanced Security Notice */}
            <Card className="bg-gradient-to-br from-[#001a2c]/60 via-[#002a3c]/60 to-[#003a4c]/60 backdrop-blur-xl border border-sky-900/40 mt-6 shadow-xl">
              <CardContent className="p-6">
                <div className="text-center">
                  <div className="w-12 h-12 bg-gradient-to-r from-green-400 to-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Shield className="h-6 w-6 text-white" />
                  </div>
                  <h3 className="font-bold text-white mb-2">Bank-Level Security</h3>
                  <p className="text-gray-300 text-sm leading-relaxed">
                    Your data is protected with 256-bit SSL encryption and industry-leading security protocols.
                    We never store your payment information and all transactions are processed securely.
                  </p>
                  <div className="flex items-center justify-center gap-4 mt-4 pt-4 border-t border-sky-900/40">
                    <div className="text-center">
                      <div className="text-sky-300 font-bold text-lg">256-bit</div>
                      <div className="text-gray-400 text-xs">SSL Encryption</div>
                    </div>
                    <div className="w-px h-8 bg-sky-900/60"></div>
                    <div className="text-center">
                      <div className="text-sky-300 font-bold text-lg">24/7</div>
                      <div className="text-gray-400 text-xs">Monitoring</div>
                    </div>
                    <div className="w-px h-8 bg-sky-900/60"></div>
                    <div className="text-center">
                      <div className="text-sky-300 font-bold text-lg">100%</div>
                      <div className="text-gray-400 text-xs">Secure</div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      <style jsx global>{`
        .shine-btn .animate-shine {
          background: linear-gradient(120deg, transparent 0%, #fff8 40%, #fff0 60%, transparent 100%);
          transform: translateX(-100%);
          animation: shine-move 2.5s linear infinite;
        }
        @keyframes shine-move {
          0% { transform: translateX(-100%); }
          100% { transform: translateX(100%); }
        }
      `}</style>
    </div>
  )
}


















