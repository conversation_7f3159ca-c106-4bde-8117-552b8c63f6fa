"use client"

import { useState, useEffect } from "react"
import { motion } from "framer-motion"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Eye, EyeOff, Lock, Mail, Shield, ArrowRight, Github, TrendingUp, Users, DollarSign, BarChart3, Globe, Zap } from "lucide-react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { login, saveToken } from "@/lib/api"
import { useAuth } from "@/lib/auth-context"
import { toast } from "@/hooks/use-toast"

export default function LoginPage() {
  const router = useRouter()
  const { setToken } = useAuth()
  const [showPassword, setShowPassword] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState("")
  const [successMessage, setSuccessMessage] = useState("")
  const [formData, setFormData] = useState({
    username: "",
    password: ""
  })

  // Check for success message from URL params
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search)
    const message = urlParams.get('message')
    if (message) {
      setSuccessMessage(message)
      // Clear the message from URL
      window.history.replaceState({}, document.title, window.location.pathname)
    }
  }, [])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
    setError("") // Clear error when user starts typing
  }

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setError("")

    try {
      const response = await login(formData)
      saveToken(response.access_token)
      setToken(response.access_token)
      toast({
        title: "Login Successful",
        description: "Welcome back! You have successfully logged in.",
        duration: 3000,
      })
      setTimeout(() => {
        router.push("/dashboard")
      }, 3000)
    } catch (error) {
      setError(error instanceof Error ? error.message : "Login failed. Please try again.")
    } finally {
      setIsLoading(false)
    }
  }

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.5,
        staggerChildren: 0.1
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.5 }
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center p-4 bg-gradient-to-br from-[#001a2c] to-[#000c14] relative overflow-hidden">
      {/* Background Trading Elements */}
      <div className="absolute inset-0 opacity-10 pointer-events-none">
        <div className="absolute top-20 left-10 w-32 h-32 bg-sky-500/20 rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 right-10 w-40 h-40 bg-blue-500/20 rounded-full blur-3xl"></div>
        <div className="absolute top-1/2 left-1/4 w-24 h-24 bg-purple-500/20 rounded-full blur-2xl"></div>
      </div>

      {/* Animated Background Bubbles */}
      <div className="absolute inset-0 overflow-hidden">
        {Array.from({ length: 20 }).map((_, i) => (
        <motion.div
            key={i}
            className="absolute rounded-full bg-sky-500/10"
            initial={{
              width: Math.random() * 100 + 20,
              height: Math.random() * 100 + 20,
              x: Math.random() * (typeof window !== 'undefined' ? window.innerWidth : 1200),
              y: (typeof window !== 'undefined' ? window.innerHeight : 800) + 100,
              opacity: Math.random() * 0.5 + 0.1,
            }}
            animate={{
              y: -200,
              opacity: 0,
            }}
            transition={{
              duration: Math.random() * 10 + 15,
              repeat: Number.POSITIVE_INFINITY,
              delay: Math.random() * 5,
            }}
          />
        ))}
              </div>

      {/* Centered Form Only */}
        <motion.div
        className="w-full max-w-md mx-auto z-10"
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          <Card className="bg-white/10 backdrop-blur-lg border-2 border-sky-500/30 shadow-2xl ring-2 ring-sky-400/10 hover:ring-sky-400/30 transition-all duration-300 overflow-hidden">
            <CardHeader className="space-y-4 pb-6">
              <motion.div variants={itemVariants}>
                <div className="w-16 h-16 rounded-2xl bg-gradient-to-br from-sky-400 to-blue-600 flex items-center justify-center mb-4 mx-auto shadow-lg ring-2 ring-sky-400/30">
                  <Shield className="h-8 w-8 text-white" />
                </div>
                <CardTitle className="text-3xl font-extrabold text-center text-white tracking-tight drop-shadow-lg">Welcome Back</CardTitle>
                <CardDescription className="text-center text-sky-200 text-lg font-medium">
                  Access your trading account and continue your journey
                </CardDescription>
                <p className="text-sky-400 text-sm mt-2 text-center font-semibold">Secure login for traders</p>
              </motion.div>
            </CardHeader>
            <CardContent className="space-y-6">

              {error && (
                <motion.div variants={itemVariants} className="bg-red-500/10 border border-red-500/20 rounded-lg p-3">
                  <p className="text-red-400 text-sm">{error}</p>
                </motion.div>
              )}

              {successMessage && (
                <motion.div variants={itemVariants} className="bg-green-500/10 border border-green-500/20 rounded-lg p-3">
                  <p className="text-green-400 text-sm">{successMessage}</p>
                </motion.div>
              )}

              <form onSubmit={handleLogin} className="space-y-4">
                <motion.div variants={itemVariants} className="space-y-2">
                  <Label htmlFor="username" className="text-white">Username</Label>
                  <div className="relative">
                    <Mail className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                    <Input
                      id="username"
                      name="username"
                      type="text"
                    value={formData.username}
                    onChange={handleInputChange}
                      placeholder="Enter your username"
                      className="pl-10 bg-[#001a2c] border-[#004c66] text-white h-12"
                      required
                    />
                  </div>
                </motion.div>

                <motion.div variants={itemVariants} className="space-y-2">
                  <Label htmlFor="password" className="text-white">Password</Label>
                  <div className="relative">
                    <Lock className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                    <Input
                      id="password"
                      name="password"
                      type={showPassword ? "text" : "password"}
                    value={formData.password}
                    onChange={handleInputChange}
                      placeholder="Enter your password"
                      className="pl-10 pr-10 bg-[#001a2c] border-[#004c66] text-white h-12"
                      required
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="icon"
                      className="absolute right-0 top-0 h-12 w-12 text-gray-400 hover:text-white"
                      onClick={() => setShowPassword(!showPassword)}
                    >
                      {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                    </Button>
                  </div>
                </motion.div>

                <motion.div variants={itemVariants} className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Checkbox id="remember" className="border-[#004c66] data-[state=checked]:bg-sky-500" />
                  <label htmlFor="remember" className="text-sm text-gray-400">
                      Remember me
                    </label>
                  </div>
                <Link href="/forgot-password" className="text-sm text-sky-400 hover:text-sky-300">
                    Forgot password?
                  </Link>
                </motion.div>

                <motion.div variants={itemVariants}>
                  <Button
                    type="submit"
                  disabled={isLoading}
                    className="w-full bg-gradient-to-r from-sky-500 to-blue-600 hover:from-sky-600 hover:to-blue-700 text-white h-12 rounded-xl font-medium"
                  >
                    {isLoading ? (
                      <motion.div
                        className="h-5 w-5 border-2 border-white border-t-transparent rounded-full"
                        animate={{ rotate: 360 }}
                        transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                      />
                    ) : (
                      <>
                        Sign In
                        <ArrowRight className="ml-2 h-4 w-4" />
                      </>
                    )}
                  </Button>
                </motion.div>
              </form>
            </CardContent>
            <CardFooter>
              <motion.p variants={itemVariants} className="text-center text-gray-400 text-sm w-full">
                <span className="inline-flex items-center gap-2 justify-center text-sky-400 font-semibold"><Shield className="h-4 w-4" /> Trusted by 10,000+ traders</span>
                <br />
                Don't have an account?{" "}
                <Link href="/auth/signup" className="text-sky-400 hover:text-sky-300 font-medium transition-colors">
                Sign Up
                </Link>
              </motion.p>
            </CardFooter>
          </Card>
        </motion.div>
    </div>
  )
} 
