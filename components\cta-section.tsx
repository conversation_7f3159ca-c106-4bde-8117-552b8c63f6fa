"use client"

import { motion } from "framer-motion"
import { Button } from "@/components/ui/button"
import Link from "next/link"

export default function CtaSection() {
  return (
    <section className="py-20 bg-gradient-to-b from-[#00253c] to-[#001a2c]">
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="max-w-4xl mx-auto bg-gradient-to-r from-sky-900/20 to-sky-800/20 backdrop-blur-sm border border-sky-500/20 rounded-2xl p-8 md:p-12 shadow-lg" // Changed to consistent sky colors
        >
          <div className="text-center">
            <h2 className="text-3xl md:text-4xl font-bold mb-4 text-white">Ready to Start Your Trading Journey?</h2>
            <p className="text-gray-400 mb-8 max-w-2xl mx-auto">
              Join thousands of successful traders who have taken their trading to the next level with our funding
              program.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/signup" passHref legacyBehavior>
                <a>
                  <Button size="lg" className="bg-sky-500 hover:bg-sky-600 text-white px-8 py-6 text-lg font-bold">
                    Get Started Now
                  </Button>
                </a>
              </Link>
              <a href="#features">
                <Button
                  size="lg"
                  variant="outline"
                  className="border-sky-500/30 text-white hover:bg-sky-500/10 px-8 py-6 text-lg font-bold"
                >
                  Learn More
                </Button>
              </a>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  )
}
