const API_BASE_URL = 'https://whales-backend-07e6b56a7fe0.herokuapp.com';

export interface SignupData {
  username: string;
  email: string;
  password: string;
  name: string;
  phone_no: string;
  country: string;
  address: string;
  referral_code?: string; // Optional field
}

export interface LoginData {
  username: string;
  password: string;
}

export interface LoginResponse {
  access_token: string;
  token_type: string;
  is_verified: boolean;
}

export interface ApiError {
  message: string;
  status?: number;
}

export interface OrderId {
  order_id: string;
  balance: string;
  username: string;
  status: string;
}

// Helper function to handle API responses
async function handleResponse<T>(response: Response): Promise<T> {
  if (!response.ok) {
    // Handle token expiration
    if (response.status === 401) {
      // Token is expired or invalid
      removeToken()
      // Redirect to login page
      if (typeof window !== 'undefined') {
        window.location.href = '/auth/login'
      }
      throw new Error('Authentication expired. Please login again.')
    }
    
    const errorData = await response.json().catch(() => ({ message: 'An error occurred' }));
    throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
  }
  return response.json();
}

// Signup API call
export async function signup(data: SignupData): Promise<any> {
  try {
    const response = await fetch(`${API_BASE_URL}/auth/signup`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });

    return handleResponse(response);
  } catch (error) {
    throw new Error(error instanceof Error ? error.message : 'Signup failed');
  }
}

// Login API call
export async function login(data: LoginData): Promise<LoginResponse> {
  try {
    const formData = new FormData();
    formData.append('username', data.username);
    formData.append('password', data.password);

    const response = await fetch(`${API_BASE_URL}/auth/login`, {
      method: 'POST',
      body: formData,
    });

    return handleResponse<LoginResponse>(response);
  } catch (error) {
    throw new Error(error instanceof Error ? error.message : 'Login failed');
  }
}

// Save token to localStorage
export function saveToken(token: string): void {
  if (typeof window !== 'undefined') {
    localStorage.setItem('access_token', token);
  }
}

// Get token from localStorage
export function getToken(): string | null {
  if (typeof window !== 'undefined') {
    return localStorage.getItem('access_token');
  }
  return null;
}

// Remove token from localStorage
export function removeToken(): void {
  if (typeof window !== 'undefined') {
    localStorage.removeItem('access_token');
  }
}

// Check if user is authenticated
export function isAuthenticated(): boolean {
  return getToken() !== null;
}

// Get all orders for user
export async function getOrders(): Promise<any[]> {
  try {
    const token = getToken();
    if (!token) {
      throw new Error('No authentication token found');
    }

    const response = await fetch(`${API_BASE_URL}/order/order_ids`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    });

    const orderIds: OrderId[] = await handleResponse<OrderId[]>(response);
    
    // Transform the backend response to match the expected format
    const transformedOrders = orderIds.map((order, index) => ({
      id: order.order_id,
      challenge_type: `Challenge ${index + 1}`,
      account_size: parseInt(order.balance || '10000'),
      status: order.status || 'Active',
      platform: "MT4", // Default platform
      created_at: new Date().toISOString(),
      profit_target: parseInt(order.balance || '10000') * 0.05, // 5% profit target
      daily_drawdown: 5,
      max_drawdown: 10,
      balance: `$${parseInt(order.balance || '10000').toLocaleString()}`,
      username: order.username || 'demo_user',
      // Add default values for frontend display
      profit: '+$0',
      openTrades: 0,
      drawdown: '0%',
      loginId: order.username || 'demo_user',
      password: 'demo123456',
      server: 'FundedWhales-Live1',
      name: `Challenge ${index + 1}`
    }));
    
    console.log('Returning real orders data from backend:', transformedOrders);
    return transformedOrders;
  } catch (error) {
    console.error('Failed to fetch orders from backend:', error);
    // Fallback to mock data if API fails
  const mockOrders = [
    {
      id: "ORD-001",
      challenge_type: "HFT Challenge",
      account_size: 50000,
      status: "Active",
      platform: "MT4",
      created_at: "2024-01-15T10:30:00Z",
      profit_target: 2500,
      daily_drawdown: 5,
      max_drawdown: 10
    },
    {
      id: "ORD-002", 
      challenge_type: "Standard Challenge",
      account_size: 100000,
      status: "Active",
      platform: "MT5",
      created_at: "2024-01-10T14:20:00Z",
      profit_target: 5000,
      daily_drawdown: 5,
      max_drawdown: 10
    },
    {
      id: "ORD-003",
      challenge_type: "Pro Challenge", 
      account_size: 200000,
      status: "Pending",
      platform: "MT4",
      created_at: "2024-01-05T09:15:00Z",
      profit_target: 10000,
      daily_drawdown: 5,
      max_drawdown: 10
    },
    {
      id: "ORD-004",
      challenge_type: "Elite Challenge",
      account_size: 500000,
      status: "Completed",
      platform: "MT5",
      created_at: "2023-12-20T16:45:00Z",
      profit_target: 25000,
      daily_drawdown: 5,
      max_drawdown: 10
    }
  ];
  
    console.log('Returning mock orders data as fallback:', mockOrders);
  return mockOrders;
  }
}

// Get order details by order ID
export async function getOrderDetails(orderId: string): Promise<any> {
  try {
    const token = getToken();
    if (!token) {
      throw new Error('No authentication token found');
    }

    // Extract numeric part from order ID (remove "FxE" prefix)
    const numericOrderId = orderId.replace(/^FxE/i, '');
    console.log(`Fetching order details for: ${orderId} -> ${numericOrderId}`);

    const response = await fetch(`${API_BASE_URL}/order/account_detail/${numericOrderId}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    });

    const orderDetails = await handleResponse<any>(response);
    console.log('Raw order details from API:', orderDetails);

    // Transform the backend response to match the expected format
    const transformedOrderDetails = {
      id: orderDetails.order_id || orderId, // Use original order ID with FxE prefix
      challenge_type: orderDetails.challenge_type || 'phase1',
      account_size: parseInt(orderDetails.account_size) || 10000,
      profit_target: orderDetails.profit_target || 0,
      platform: orderDetails.platform?.toUpperCase() || 'MT4',
      status: orderDetails.status || 'Active',
      // Map all the fields from the API response (no default values for optional fields)
      server: orderDetails.server || null,
      username: orderDetails.username || null,
      platform_login: orderDetails.platform_login || null,
      platform_password: orderDetails.platform_password || null,
      session_id: orderDetails.session_id || null,
      terminal_id: orderDetails.terminal_id || null,
      created_at: new Date().toISOString(),
      start_date: new Date().toISOString().split('T')[0],
      end_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 30 days from now
      daily_drawdown: 5,
      max_drawdown: 10,
      leverage: "1:100",
      // Add additional fields for frontend display
      balance: `$${parseInt(orderDetails.account_size || '10000').toLocaleString()}`,
      profit: '+$0',
      openTrades: 0,
      drawdown: '0%',
      name: `${orderDetails.challenge_type || 'Challenge'} Account`
    };
    
    console.log('Transformed order details for frontend:', transformedOrderDetails);
    return transformedOrderDetails;
  } catch (error) {
    console.error('Failed to fetch order details from backend for order:', orderId, 'Error:', error);
    // Fallback to mock data if API fails
    const mockOrderDetails = {
      id: orderId,
      challenge_type: "phase1",
      account_size: 10000,
      profit_target: 0,
      platform: "MT4",
      status: "Active",
      server: null,
      username: "demo_user",
      platform_login: null,
      platform_password: null,
      session_id: null,
      terminal_id: null,
    created_at: "2024-01-01T00:00:00Z",
    start_date: "2024-01-01",
    end_date: "2024-02-01",
    daily_drawdown: 5,
    max_drawdown: 10,
      leverage: "1:100",
      balance: "$100,000",
      profit: "+$0",
      openTrades: 0,
      drawdown: "0%",
      name: "Default Challenge Account"
  };
  
    console.log('Returning mock order details as fallback for ID:', orderId, mockOrderDetails);
  return mockOrderDetails;
  }
}

// Get user profile information
export async function getUserProfile(): Promise<any> {
  try {
    const token = getToken();
    if (!token) {
      throw new Error('No authentication token found');
    }

    const response = await fetch(`${API_BASE_URL}/auth/me`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    });

    return handleResponse(response);
  } catch (error) {
    console.error('Failed to fetch user profile:', error);
    // Return null if profile endpoint doesn't exist or fails
    return null;
  }
}

// Create new order
export async function createOrder(data: {
  email: string;
  challenge_type: string;
  account_size: string;
  platform: string;
  payment_method: string;
  txid: string;
  image: File;
}): Promise<any> {
  try {
    const token = getToken();
    if (!token) {
      throw new Error('No authentication token found');
    }

    const formData = new FormData();
    formData.append('email', data.email);
    formData.append('challenge_type', data.challenge_type);
    formData.append('account_size', data.account_size);
    formData.append('platform', data.platform);
    formData.append('payment_method', data.payment_method);
    formData.append('txid', data.txid);
    formData.append('image', data.image);

    const response = await fetch(`${API_BASE_URL}/order/order`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
      },
      body: formData,
    });

    return handleResponse(response);
  } catch (error) {
    throw new Error(error instanceof Error ? error.message : 'Failed to create order');
  }
}