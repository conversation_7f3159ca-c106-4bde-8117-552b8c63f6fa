"use client"

import { useEffect, useState, useRef } from "react"
import { motion } from "framer-motion"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { BarChart3, Loader2, TrendingUp } from "lucide-react"

// TradingView Widget Component
function TradingViewWidget({
  containerId,
  scriptSrc,
  config,
  widgetName,
  height = "400px"
}: {
  containerId: string
  scriptSrc: string
  config: any
  widgetName: string
  height?: string
}) {
  const containerRef = useRef<HTMLDivElement>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [hasError, setHasError] = useState(false)

  useEffect(() => {
    if (!containerRef.current) return

    const container = containerRef.current

    // Clear any existing content
    container.innerHTML = ''

    // Create TradingView widget structure
    const widgetContainer = document.createElement('div')
    widgetContainer.className = 'tradingview-widget-container'
    widgetContainer.style.height = '100%'
    widgetContainer.style.width = '100%'

    const widgetContent = document.createElement('div')
    widgetContent.className = 'tradingview-widget-container__widget'
    widgetContent.style.height = 'calc(100% - 32px)'
    widgetContent.style.width = '100%'

    const script = document.createElement('script')
    script.type = 'text/javascript'
    script.src = scriptSrc
    script.async = true
    script.innerHTML = JSON.stringify(config)

    script.onload = () => {
      console.log(`✅ ${widgetName} loaded successfully`)
      setIsLoading(false)
      setHasError(false)
    }

    script.onerror = () => {
      console.error(`❌ Failed to load ${widgetName}`)
      setIsLoading(false)
      setHasError(true)
    }

    widgetContainer.appendChild(widgetContent)
    widgetContainer.appendChild(script)
    container.appendChild(widgetContainer)

    return () => {
      if (container) {
        container.innerHTML = ''
      }
    }
  }, [scriptSrc, config, widgetName])

  return (
    <div
      ref={containerRef}
      id={containerId}
      className="w-full bg-[#002a3c]/50 rounded-lg border border-sky-500/10 relative"
      style={{ height, minHeight: height }}
    >
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="flex items-center space-x-2 text-sky-400">
            <Loader2 className="h-6 w-6 animate-spin" />
            <span>Loading {widgetName}...</span>
          </div>
        </div>
      )}
      {hasError && (
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="text-center text-gray-400 p-4">
            <p className="mb-2">⚠️ {widgetName} unavailable</p>
            <p className="text-sm">Please check your internet connection and refresh the page</p>
          </div>
        </div>
      )}
    </div>
  )
}

export default function SymbolsPage() {
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  // Widget configurations
  const tickerConfig = {
    "symbols": [
      {"proName": "FX:EURUSD", "title": "EUR/USD"},
      {"proName": "FX:GBPUSD", "title": "GBP/USD"},
      {"proName": "FX:USDJPY", "title": "USD/JPY"},
      {"proName": "FX:USDCHF", "title": "USD/CHF"},
      {"proName": "FX:AUDUSD", "title": "AUD/USD"},
      {"proName": "FX:USDCAD", "title": "USD/CAD"},
      {"proName": "FX:NZDUSD", "title": "NZD/USD"},
      {"proName": "FX:EURGBP", "title": "EUR/GBP"}
    ],
    "showSymbolLogo": true,
    "colorTheme": "dark",
    "isTransparent": false,
    "displayMode": "adaptive",
    "locale": "en"
  }

  const chartConfig = {
    "width": "100%",
    "height": "500",
    "symbol": "FX:EURUSD",
    "interval": "1",
    "timezone": "Etc/UTC",
    "theme": "dark",
    "style": "1",
    "locale": "en",
    "toolbar_bg": "#f1f3f6",
    "enable_publishing": false,
    "withdateranges": true,
    "range": "1D",
    "hide_side_toolbar": false,
    "allow_symbol_change": true,
    "details": true,
    "hotlist": true,
    "calendar": true,
    "studies": ["STD;SMA"],
    "container_id": "tradingview_chart",
    "backgroundColor": "rgba(0, 42, 60, 0.9)",
    "gridColor": "rgba(135, 206, 235, 0.1)",
    "hide_top_toolbar": false,
    "hide_legend": false,
    "save_image": false,
    "watchlist": ["FX:EURUSD", "FX:GBPUSD", "FX:USDJPY", "FX:USDCHF", "FX:AUDUSD", "FX:USDCAD", "FX:NZDUSD", "FX:EURGBP"]
  }



  // Prevent hydration issues by not rendering until mounted
  if (!mounted) {
    return (
      <div className="min-h-screen p-6 space-y-6">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="flex items-center space-x-2 text-sky-400">
            <Loader2 className="h-8 w-8 animate-spin" />
            <span className="text-lg">Loading Market Analysis...</span>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen p-6 space-y-6">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <div className="flex items-center space-x-3 mb-6">
          <div className="p-2 bg-gradient-to-r from-sky-500/20 to-sky-400/20 rounded-lg border border-sky-500/30">
            <TrendingUp className="h-6 w-6 text-sky-400" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-white">Market Analysis</h1>
            <p className="text-gray-400">Real-time trading charts and market data</p>
          </div>
        </div>

        {/* Real-time Ticker */}
        <Card className="bg-gradient-to-br from-[#002a3c]/90 to-[#001e30]/90 border-sky-500/20 mb-6">
          <CardContent className="p-4">
            <div 
              id="tradingview-ticker-widget" 
              className="w-full min-h-[80px] bg-[#002a3c]/50 rounded-lg border border-sky-500/10"
            />
          </CardContent>
        </Card>

        {/* Live Charts Section - No tabs needed since we only have one section */}
        <div className="space-y-6">

          <Card className="bg-gradient-to-br from-[#002a3c]/90 to-[#001e30]/90 border-sky-500/20">
            <CardHeader>
              <CardTitle className="text-white flex items-center">
                <BarChart3 className="h-5 w-5 mr-2 text-sky-400" />
                Real-time Trading Charts
              </CardTitle>
              <CardDescription className="text-gray-400">
                Advanced live charts with real-time price data and technical analysis tools
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div
                id="tradingview-symbol-widget"
                className="w-full min-h-[500px] bg-[#002a3c]/50 rounded-lg border border-sky-500/10 relative"
              >
                {loading && (
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="flex items-center space-x-2 text-sky-400">
                      <Loader2 className="h-6 w-6 animate-spin" />
                      <span>Loading TradingView widgets...</span>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </motion.div>
    </div>
  )
}
