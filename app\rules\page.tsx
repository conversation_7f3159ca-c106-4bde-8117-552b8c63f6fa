import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { AlertCircle, CheckCircle, XCircle } from "lucide-react"

export default function RulesPage() {
  const instantRules = [
    "NEWS TRADING: Not Allowed",
    "OVERNIGHT HOLDING: Allowed",
    "MAX TOTAL LOSS: 5%",
    "PROFIT SPLIT: 90%",
    "CONSISTENCY RULE: 30%",
    "WEEKEND HOLDING: Not Allowed",
    "Leverage: 1:100",
  ];
  const twoStepRules = [
    "Profit Target Phase-1: 10%",
    "Profit Target Phase-2: 5%",
    "Daily Drawdown: 4%",
    "Max Drawdown: 10% (Static)",
    "Profit Share: 70% to 100%",
    "Min Trading Days: 5",
    "Min Trades: 5",
    "Leverage: 1:100",
  ];
  const oneStepRules = [
    "Profit Target: 10%",
    "Daily Drawdown: 4%",
    "Max Drawdown: 10% (Static)",
    "Profit Share: 70% to 100%",
    "Min Trading Days: 5",
    "Min Trades: 5",
    "Leverage: 1:100",
  ];
  const hftRules = [
    "Profit Target: 8%",
    "Daily Drawdown: 5%",
    "Max Drawdown: 10% (Static)",
    "Profit Share after Passing: 50% to 100%",
    "EA Trading: Allowed",
    "Min Trading Days: 1",
    "Leverage: 1:100",
  ];

  return (
    <main className="flex-1 py-20 bg-black">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h1 className="text-4xl md:text-5xl font-bold mb-4 text-white">Trading Rules</h1>
          <p className="text-gray-400 max-w-2xl mx-auto">
            Our trading rules are designed to promote responsible trading and risk management. Please review them carefully before starting your challenge.
          </p>
        </div>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <Card className="bg-gray-900 border-gray-800">
            <CardHeader>
              <CardTitle className="flex items-center text-white">
                INSTANT
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-3">
                {instantRules.map((rule, index) => (
                  <li key={index} className="text-gray-300 flex items-start">
                    <span className="mr-2">•</span>
                    <span>{rule}</span>
                  </li>
                ))}
              </ul>
            </CardContent>
          </Card>
          <Card className="bg-gray-900 border-gray-800">
            <CardHeader>
              <CardTitle className="flex items-center text-white">
                2 STEP
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-3">
                {twoStepRules.map((rule, index) => (
                  <li key={index} className="text-gray-300 flex items-start">
                    <span className="mr-2">•</span>
                    <span>{rule}</span>
                  </li>
                ))}
              </ul>
            </CardContent>
          </Card>
          <Card className="bg-gray-900 border-gray-800">
            <CardHeader>
              <CardTitle className="flex items-center text-white">
                1 STEP
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-3">
                {oneStepRules.map((rule, index) => (
                  <li key={index} className="text-gray-300 flex items-start">
                    <span className="mr-2">•</span>
                    <span>{rule}</span>
                  </li>
                ))}
              </ul>
            </CardContent>
          </Card>
          <Card className="bg-gray-900 border-gray-800">
            <CardHeader>
              <CardTitle className="flex items-center text-white">
                HFT
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-3">
                {hftRules.map((rule, index) => (
                  <li key={index} className="text-gray-300 flex items-start">
                    <span className="mr-2">•</span>
                    <span>{rule}</span>
                  </li>
                ))}
              </ul>
            </CardContent>
          </Card>
        </div>
      </div>
    </main>
  )
}
