# Meta Pixel Implementation Guide

## Overview
This document outlines the complete Meta Pixel implementation for the Funded Whales trading platform. The Meta Pixel (ID: `****************`) is now properly integrated across all pages and tracks user interactions.

## Implementation Details

### 1. Root Layout Integration (`app/layout.tsx`)
- **Meta Pixel Script**: Implemented using Next.js `Script` component with `afterInteractive` strategy
- **NoScript Fallback**: Included for users with JavaScript disabled
- **Global Coverage**: Automatically applies to all pages in the application

### 2. Client-Side Route Tracking (`components/meta-pixel-tracker.tsx`)
- **Route Change Tracking**: Automatically tracks PageView events on client-side navigation
- **Next.js Router Integration**: Uses `usePathname` and `useSearchParams` to detect route changes
- **Development Logging**: Includes console logging for debugging in development mode

### 3. Meta Pixel Utility Library (`lib/meta-pixel.ts`)
- **Centralized Functions**: All Meta Pixel functions in one place
- **Type Safety**: TypeScript declarations for window.fbq
- **Standard Events**: Pre-configured functions for common e-commerce events
- **Error Handling**: Safe execution with proper checks

### 4. Event Tracking Implementation

#### Purchase Events (`app/dashboard/buy-account/page.tsx`)
- **InitiateCheckout**: Triggered when user selects payment method
- **Purchase**: Triggered when order is successfully placed
- **Value Tracking**: Includes actual purchase amount and currency

#### Registration Events
- **CompleteRegistration**: Triggered on successful signup
- **Implemented in**:
  - `app/auth/signup/page.tsx`
  - `app/signup/page.tsx`

## Pages Covered

### Main Application Pages
✅ **Home Page** (`/`) - PageView tracking
✅ **Rules Page** (`/rules`) - PageView tracking
✅ **Support Page** (`/support`) - PageView tracking
✅ **Challenge Page** (`/challenge`) - PageView tracking

### Authentication Pages
✅ **Login Pages** (`/auth/login`, `/login`) - PageView tracking
✅ **Signup Pages** (`/auth/signup`, `/signup`) - PageView + CompleteRegistration tracking

### Dashboard Pages
✅ **Dashboard** (`/dashboard`) - PageView tracking
✅ **Buy Account** (`/dashboard/buy-account`) - PageView + InitiateCheckout + Purchase tracking
✅ **Trading Accounts** (`/dashboard/accounts`) - PageView tracking
✅ **KYC Verification** (`/dashboard/kyc`) - PageView tracking
✅ **Withdrawals** (`/dashboard/withdrawals`) - PageView tracking
✅ **Market News** (`/dashboard/news`) - PageView tracking
✅ **Billing** (`/dashboard/billing`) - PageView tracking
✅ **Certificates** (`/dashboard/certificates`) - PageView tracking
✅ **Help** (`/dashboard/help`) - PageView tracking
✅ **Referrals** (`/dashboard/referrals`) - PageView tracking
✅ **Rewards** (`/dashboard/rewards`) - PageView tracking
✅ **Settings** (`/dashboard/settings`) - PageView tracking
✅ **Support** (`/dashboard/support`) - PageView tracking
✅ **Symbols** (`/dashboard/symbols`) - PageView tracking

### Admin Pages
✅ **Admin Dashboard** (`/admin`) - PageView tracking
✅ **User Management** (`/admin/users`) - PageView tracking
✅ **All Orders** (`/admin/orders`) - PageView tracking
✅ **Running Orders** (`/admin/orders/running`) - PageView tracking
✅ **Completed Orders** (`/admin/orders/completed`) - PageView tracking
✅ **Passed Orders** (`/admin/orders/passed`) - PageView tracking
✅ **Support Tickets** (`/admin/support`) - PageView tracking
✅ **Settings** (`/admin/settings`) - PageView tracking

## Testing and Verification

### Test Page (`/test-pixel`)
A comprehensive test page has been created to verify Meta Pixel functionality:
- **Real-time Status**: Shows if Meta Pixel is loaded
- **Event Testing**: Buttons to test all implemented events
- **Event Logging**: Real-time log of triggered events
- **Debug Information**: Pixel ID, version, and availability status

### Verification Methods

1. **Browser Developer Tools**
   - Open Network tab
   - Filter by "facebook" or "fbevents"
   - Look for successful requests to Facebook's servers

2. **Meta Pixel Helper Extension**
   - Install Chrome extension
   - Visit any page to see pixel events
   - Green checkmark indicates successful tracking

3. **Facebook Events Manager**
   - Log into Facebook Business Manager
   - Navigate to Events Manager
   - View real-time events and data

4. **Console Testing**
   ```javascript
   // Check if pixel is loaded
   console.log(window.fbq);
   
   // Manually trigger test event
   window.fbq('track', 'PageView');
   ```

## Event Types Implemented

| Event Type | Trigger | Pages | Parameters |
|------------|---------|-------|------------|
| `PageView` | Page load/navigation | All pages | None |
| `Purchase` | Successful order | Buy Account | value, currency |
| `InitiateCheckout` | Payment method selection | Buy Account | value, currency |
| `CompleteRegistration` | Successful signup | Signup pages | None |
| `Lead` | Available via utility | Manual trigger | None |
| `ViewContent` | Available via utility | Manual trigger | content_name |
| `AddToCart` | Available via utility | Manual trigger | value, currency |

## Technical Implementation

### Next.js Script Component
```tsx
<Script
  id="meta-pixel"
  strategy="afterInteractive"
  dangerouslySetInnerHTML={{
    __html: `
      !function(f,b,e,v,n,t,s)
      {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
      n.callMethod.apply(n,arguments):n.queue.push(arguments)};
      if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
      n.queue=[];t=b.createElement(e);t.async=!0;
      t.src=v;s=b.getElementsByTagName(e)[0];
      s.parentNode.insertBefore(t,s)}(window, document,'script',
      'https://connect.facebook.net/en_US/fbevents.js');
      fbq('init', '****************');
      fbq('track', 'PageView');
    `,
  }}
/>
```

### Route Change Tracking
```tsx
useEffect(() => {
  trackPageView()
  if (process.env.NODE_ENV === 'development') {
    console.log('Meta Pixel: PageView tracked for', pathname)
  }
}, [pathname, searchParams])
```

## Troubleshooting

### Common Issues
1. **Pixel not loading**: Check network requests and console errors
2. **Events not firing**: Verify function calls and parameters
3. **Duplicate events**: Ensure proper useEffect dependencies

### Debug Mode
Development mode includes console logging for all PageView events to help with debugging.

## Maintenance

### Adding New Events
1. Add function to `lib/meta-pixel.ts`
2. Import and call in appropriate component
3. Test using the test page

### Updating Pixel ID
Update the `PIXEL_ID` constant in `lib/meta-pixel.ts` if needed.

## Compliance Notes
- NoScript fallback included for accessibility
- GDPR/privacy considerations should be handled separately
- Consider implementing consent management if required by jurisdiction
