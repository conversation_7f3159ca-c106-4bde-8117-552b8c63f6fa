declare global {
  interface Window {
    fbq: any
    _fbq: any
  }
}

export const PIXEL_ID = '1287306579402842'

// Initialize Meta Pixel
export const initMetaPixel = () => {
  if (typeof window === 'undefined') return

  // Check if fbq is already loaded
  if (window.fbq) return

  // Initialize fbq
  window.fbq = function(...args: any[]) {
    if (window.fbq.callMethod) {
      window.fbq.callMethod.apply(window.fbq, args)
    } else {
      window.fbq.queue.push(args)
    }
  }

  if (!window._fbq) window._fbq = window.fbq
  window.fbq.push = window.fbq
  window.fbq.loaded = true
  window.fbq.version = '2.0'
  window.fbq.queue = []

  // Load the pixel script
  const script = document.createElement('script')
  script.async = true
  script.src = 'https://connect.facebook.net/en_US/fbevents.js'
  const firstScript = document.getElementsByTagName('script')[0]
  firstScript.parentNode?.insertBefore(script, firstScript)

  // Initialize pixel with ID
  window.fbq('init', PIXEL_ID)
  window.fbq('track', 'PageView')
}

// Track page view
export const trackPageView = () => {
  if (typeof window !== 'undefined' && window.fbq) {
    window.fbq('track', 'PageView')
  }
}

// Track custom events
export const trackEvent = (eventName: string, parameters?: Record<string, any>) => {
  if (typeof window !== 'undefined' && window.fbq) {
    if (parameters) {
      window.fbq('track', eventName, parameters)
    } else {
      window.fbq('track', eventName)
    }
  }
}

// Track standard events
export const trackPurchase = (value: number, currency: string = 'USD') => {
  trackEvent('Purchase', { value, currency })
}

export const trackLead = () => {
  trackEvent('Lead')
}

export const trackCompleteRegistration = () => {
  trackEvent('CompleteRegistration')
}

export const trackViewContent = (contentName?: string) => {
  trackEvent('ViewContent', contentName ? { content_name: contentName } : undefined)
}

export const trackAddToCart = (value?: number, currency: string = 'USD') => {
  trackEvent('AddToCart', value ? { value, currency } : undefined)
}

export const trackInitiateCheckout = (value?: number, currency: string = 'USD') => {
  trackEvent('InitiateCheckout', value ? { value, currency } : undefined)
}
