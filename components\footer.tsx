"use client"

import { motion } from "framer-motion"
import { Facebook, Instagram } from "lucide-react"
import Link from "next/link"
import Image from "next/image"

export default function Footer() {
  const socialLinks = [
    {
      name: "Discord",
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
          className="w-8 h-8"
        >
          <path d="M9 12a1 1 0 1 0 2 0a1 1 0 1 0 -2 0" />
          <path d="M15 12a1 1 0 1 0 2 0a1 1 0 1 0 -2 0" />
          <path d="M8.5 17c0 1 -1.356 3 -1.832 3c-1.429 0 -2.698 -1.667 -3.333 -3c-.635 -1.667 -.476 -5.833 1.428 -11.5c1.388 -1.015 3.025 -1.34 4.5 -1.5l.238 2.5" />
          <path d="M14.5 17c0 1 1.5 3 2 3c1.5 0 2.764 -1.667 3.5 -3c.736 -1.333 .476 -5.833 -1.5 -11.5c-1.457 -1.015 -3.248 -1.34 -4.5 -1.5l-.25 2.5" />
          <path d="M7 16.5c3.5 1 6.5 1 10 0" />
        </svg>
      ),
      url: "https://discord.gg/hMWxAwegnc",
    },
    {
      name: "Facebook",
      icon: <Facebook className="w-8 h-8" />, 
      url: "https://www.facebook.com/share/1Ehxffb3rA/",
    },
    {
      name: "Instagram",
      icon: <Instagram className="w-8 h-8" />, 
      url: "https://www.instagram.com/fundedwhales?igsh=MTYxb2QxdnppajdzMg==",
    },
    {
      name: "Telegram",
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
          className="w-8 h-8"
        >
          <path d="M11.944 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12 12 12 0 0 0 12-12A12 12 0 0 0 12 0a12 12 0 0 0-.056 0zm4.962 7.224c.1-.002.321.023.465.14a.506.506 0 0 1 .171.325c.016.093.036.306.02.472-.18 1.898-.96 6.502-1.36 8.627-.168.9-.499 1.201-.82 1.23-.696.065-1.225-.46-1.9-.902-1.056-.693-1.653-1.124-2.678-1.8-1.185-.78-.417-1.21.258-1.91.177-.184 3.247-2.977 3.307-3.23.007-.032.014-.15-.056-.212s-.174-.041-.249-.024c-.106.024-1.793 1.14-5.061 3.345-.48.33-.913.49-1.302.48-.428-.008-1.252-.241-1.865-.44-.752-.245-1.349-.374-1.297-.789.027-.216.325-.437.893-.663 3.498-1.524 5.83-2.529 6.998-3.014 3.332-1.386 4.025-1.627 4.476-1.635z" />
        </svg>
      ),
      url: "https://t.me/fundedwhales1",
    },
  ]

  const footerLinks = [
    {
      title: "Explore",
      links: [
        { name: "Features", href: "#features" },
        { name: "Pricing", href: "#pricing" },
        { name: "How It Works", href: "#how-it-works" },
        { name: "Testimonials", href: "#testimonials" },
        { name: "FAQ", href: "#faq" },
        { name: "Contact", href: "#contact" },
      ],
    },
    {
      title: "Platform",
      links: [
        { name: "Dashboard", href: "/dashboard" },
        { name: "Buy Account", href: "/dashboard/buy-account" },
        { name: "Market News", href: "/dashboard/news" },
      ],
    },
    {
      title: "Support",
      links: [
        { name: "<EMAIL>", href: "mailto:<EMAIL>" },
      ],
    },
  ]

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 10 },
    visible: { opacity: 1, y: 0 },
    hover: {
      scale: 1.1,
      color: "#38BDF8", // Changed from teal to sky
      transition: { duration: 0.3 },
    },
  }

  return (
    <footer className="bg-gradient-to-b from-[#001525] via-[#001a2c] to-[#002235] border-t-2 border-sky-900/40 shadow-inner">
      <div className="container mx-auto px-4 py-16">
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-10">
          {/* Logo and description */}
          <div className="col-span-1 flex flex-col gap-4">
            <Link href="/" className="flex items-center mb-2">
              <Image
                src="https://res.cloudinary.com/dufcjjaav/image/upload/v1751695664/logo-removebg-preview_eqaddm.png"
                alt="Funded Whales Logo"
                width={48}
                height={48}
                className="w-12 h-12 mr-3 drop-shadow-lg"
              />
              <span className="text-2xl font-extrabold text-white underwater-text tracking-tight">
                FUNDED<span className="text-sky-400">WHALES</span>
              </span>
            </Link>
            <span className="text-sky-400 font-semibold text-sm mb-2">Fueling Traders. Funding Dreams.</span>
            {/* Social Media Section */}
            <motion.div
              variants={containerVariants}
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true }}
              className="flex flex-wrap gap-3 mt-2"
            >
              {socialLinks.map((social, index) => (
                <motion.a
                  key={index}
                  variants={itemVariants}
                  whileHover="hover"
                  href={social.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  aria-label={`Follow us on ${social.name}`}
                  className="flex items-center justify-center w-12 h-12 rounded-full bg-[#002a3c] text-gray-300 hover:text-sky-400 hover:bg-sky-900/30 border border-sky-900/30 shadow hover:shadow-lg transition-all duration-300"
                >
                  {social.icon}
                </motion.a>
              ))}
            </motion.div>
          </div>

          {/* Footer links */}
          {footerLinks.map((section, i) => (
            <div key={i} className="col-span-1">
              <h3 className="text-white font-bold mb-4 tracking-wide uppercase text-sm letter-spacing-1">{section.title}</h3>
              <ul className="space-y-3">
                {section.links.map((link, j) => (
                  <li key={j}>
                    <Link href={link.href} className="text-gray-400 hover:text-sky-400 font-medium transition-colors duration-200">
                      {section.title === 'Support' ? (
                        <span className="inline-flex items-center gap-2 text-base font-semibold">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-sky-400" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 12H8m8 0a4 4 0 11-8 0 4 4 0 018 0zm0 0v4m0-4V8" /></svg>
                          {link.name}
                        </span>
                      ) : link.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>

        {/* Disclaimer */}
        <div className="mt-10">
          <p className="text-gray-400 text-xs md:text-sm tracking-wide text-center w-full mb-4 md:mb-0">
            FundedWhales is a learning and performance-focused platform. We don’t provide real trading accounts, financial services, or investment advice. All activities are simulated for educational use only and do not guarantee capital access or financial outcomes.
          </p>
        </div>
        <div className="mt-4 pt-8 border-t-2 border-sky-900/30 flex flex-col md:flex-row justify-between items-center gap-4">
          <p className="text-gray-500 text-xs md:text-sm tracking-wide">
            © {new Date().getFullYear()} Funded Whales. All rights reserved.
          </p>
          <p className="text-gray-500 text-xs md:text-sm tracking-wide">
            Designed with passion for traders worldwide.
          </p>
        </div>
      </div>
    </footer>
  )
}
