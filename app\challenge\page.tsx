import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Check } from "lucide-react"

export default function ChallengePage() {
  const accountTypes = [
    {
      id: "instant",
      name: "Instant",
      description: "Skip the evaluation and get funded immediately",
      plans: [
        {
          capital: "$10,000",
          price: "$299",
          profitTarget: "None",
          maxDrawdown: "5%",
          profitSplit: "90%",
          features: [
            "NEWS TRADING: Not Allowed",
            "OVERNIGHT HOLDING: Allowed",
            "MAX TOTAL LOSS: 5%",
            "PROFIT SPLIT: 90%",
            "CONSISTENCY RULE: 30%",
            "WEEKEND HOLDING: Not Allowed",
            "Leverage: 1:100",
          ],
          popular: false,
        },
        {
          capital: "$50,000",
          price: "$999",
          profitTarget: "None",
          maxDrawdown: "5%",
          profitSplit: "90%",
          features: [
            "NEWS TRADING: Not Allowed",
            "OVERNIGHT HOLDING: Allowed",
            "MAX TOTAL LOSS: 5%",
            "PROFIT SPLIT: 90%",
            "CONSISTENCY RULE: 30%",
            "WEEKEND HOLDING: Not Allowed",
            "Leverage: 1:100",
          ],
          popular: true,
        },
        {
          capital: "$100,000",
          price: "$1,899",
          profitTarget: "None",
          maxDrawdown: "5%",
          profitSplit: "90%",
          features: [
            "NEWS TRADING: Not Allowed",
            "OVERNIGHT HOLDING: Allowed",
            "MAX TOTAL LOSS: 5%",
            "PROFIT SPLIT: 90%",
            "CONSISTENCY RULE: 30%",
            "WEEKEND HOLDING: Not Allowed",
            "Leverage: 1:100",
          ],
          popular: false,
        },
        {
          capital: "$200,000",
          price: "$3,599",
          profitTarget: "None",
          maxDrawdown: "5%",
          profitSplit: "90%",
          features: [
            "NEWS TRADING: Not Allowed",
            "OVERNIGHT HOLDING: Allowed",
            "MAX TOTAL LOSS: 5%",
            "PROFIT SPLIT: 90%",
            "CONSISTENCY RULE: 30%",
            "WEEKEND HOLDING: Not Allowed",
            "Leverage: 1:100",
          ],
          popular: false,
        },
      ],
    },
    {
      id: "step2",
      name: "2 Step",
      description: "Two-step evaluation process with clear rules.",
      plans: [
        {
          capital: "$10,000",
          price: "$79",
          profitTarget: "Phase-1: 10% | Phase-2: 5%",
          maxDrawdown: "10% (Static)",
          profitSplit: "70% to 100%",
          features: [
            "Profit Target Phase-1: 10%",
            "Profit Target Phase-2: 5%",
            "Daily Drawdown: 4%",
            "Max Drawdown: 10% (Static)",
            "Profit Share: 70% to 100%",
            "Min Trading Days: 5",
            "Min Trades: 5",
            "Leverage: 1:100",
          ],
          popular: false,
        },
        {
          capital: "$50,000",
          price: "$199",
          profitTarget: "Phase-1: 10% | Phase-2: 5%",
          maxDrawdown: "10% (Static)",
          profitSplit: "70% to 100%",
          features: [
            "Profit Target Phase-1: 10%",
            "Profit Target Phase-2: 5%",
            "Daily Drawdown: 4%",
            "Max Drawdown: 10% (Static)",
            "Profit Share: 70% to 100%",
            "Min Trading Days: 5",
            "Min Trades: 5",
            "Leverage: 1:100",
          ],
          popular: true,
        },
        {
          capital: "$100,000",
          price: "$399",
          profitTarget: "Phase-1: 10% | Phase-2: 5%",
          maxDrawdown: "10% (Static)",
          profitSplit: "70% to 100%",
          features: [
            "Profit Target Phase-1: 10%",
            "Profit Target Phase-2: 5%",
            "Daily Drawdown: 4%",
            "Max Drawdown: 10% (Static)",
            "Profit Share: 70% to 100%",
            "Min Trading Days: 5",
            "Min Trades: 5",
            "Leverage: 1:100",
          ],
          popular: false,
        },
        {
          capital: "$200,000",
          price: "$1,249",
          profitTarget: "Phase-1: 10% | Phase-2: 5%",
          maxDrawdown: "10% (Static)",
          profitSplit: "70% to 100%",
          features: [
            "Profit Target Phase-1: 10%",
            "Profit Target Phase-2: 5%",
            "Daily Drawdown: 4%",
            "Max Drawdown: 10% (Static)",
            "Profit Share: 70% to 100%",
            "Min Trading Days: 5",
            "Min Trades: 5",
            "Leverage: 1:100",
          ],
          popular: false,
        },
      ],
    },
    {
      id: "step1",
      name: "1 Step",
      description: "One-step evaluation process with clear rules.",
      plans: [
        {
          capital: "$10,000",
          price: "$99",
          profitTarget: "10%",
          maxDrawdown: "10% (Static)",
          profitSplit: "70% to 100%",
          features: [
            "Profit Target: 10%",
            "Daily Drawdown: 4%",
            "Max Drawdown: 10% (Static)",
            "Profit Share: 70% to 100%",
            "Min Trading Days: 5",
            "Min Trades: 5",
            "Leverage: 1:100",
          ],
          popular: false,
        },
        {
          capital: "$50,000",
          price: "$249",
          profitTarget: "10%",
          maxDrawdown: "10% (Static)",
          profitSplit: "70% to 100%",
          features: [
            "Profit Target: 10%",
            "Daily Drawdown: 4%",
            "Max Drawdown: 10% (Static)",
            "Profit Share: 70% to 100%",
            "Min Trading Days: 5",
            "Min Trades: 5",
            "Leverage: 1:100",
          ],
          popular: true,
        },
        {
          capital: "$100,000",
          price: "$499",
          profitTarget: "10%",
          maxDrawdown: "10% (Static)",
          profitSplit: "70% to 100%",
          features: [
            "Profit Target: 10%",
            "Daily Drawdown: 4%",
            "Max Drawdown: 10% (Static)",
            "Profit Share: 70% to 100%",
            "Min Trading Days: 5",
            "Min Trades: 5",
            "Leverage: 1:100",
          ],
          popular: false,
        },
        {
          capital: "$200,000",
          price: "$999",
          profitTarget: "10%",
          maxDrawdown: "10% (Static)",
          profitSplit: "70% to 100%",
          features: [
            "Profit Target: 10%",
            "Daily Drawdown: 4%",
            "Max Drawdown: 10% (Static)",
            "Profit Share: 70% to 100%",
            "Min Trading Days: 5",
            "Min Trades: 5",
            "Leverage: 1:100",
          ],
          popular: false,
        },
      ],
    },
    {
      id: "hft",
      name: "HFT",
      description: "High-Frequency Trading Challenge with unique rules.",
      plans: [
        {
          capital: "$10,000",
          price: "$18",
          profitTarget: "8%",
          maxDrawdown: "10% (Static)",
          profitSplit: "50% to 100% (after passing)",
          features: [
            "Profit Target: 8%",
            "Daily Drawdown: 5%",
            "Max Drawdown: 10% (Static)",
            "Profit Share after Passing: 50% to 100%",
            "EA Trading: Allowed",
            "Min Trading Days: 1",
            "Leverage: 1:100",
          ],
          popular: false,
        },
        {
          capital: "$50,000",
          price: "$80",
          profitTarget: "8%",
          maxDrawdown: "10% (Static)",
          profitSplit: "50% to 100% (after passing)",
          features: [
            "Profit Target: 8%",
            "Daily Drawdown: 5%",
            "Max Drawdown: 10% (Static)",
            "Profit Share after Passing: 50% to 100%",
            "EA Trading: Allowed",
            "Min Trading Days: 1",
            "Leverage: 1:100",
          ],
          popular: true,
        },
        {
          capital: "$100,000",
          price: "$290",
          profitTarget: "8%",
          maxDrawdown: "10% (Static)",
          profitSplit: "50% to 100% (after passing)",
          features: [
            "Profit Target: 8%",
            "Daily Drawdown: 5%",
            "Max Drawdown: 10% (Static)",
            "Profit Share after Passing: 50% to 100%",
            "EA Trading: Allowed",
            "Min Trading Days: 1",
            "Leverage: 1:100",
          ],
          popular: false,
        },
        {
          capital: "$200,000",
          price: "$819",
          profitTarget: "8%",
          maxDrawdown: "10% (Static)",
          profitSplit: "50% to 100% (after passing)",
          features: [
            "Profit Target: 8%",
            "Daily Drawdown: 5%",
            "Max Drawdown: 10% (Static)",
            "Profit Share after Passing: 50% to 100%",
            "EA Trading: Allowed",
            "Min Trading Days: 1",
            "Leverage: 1:100",
          ],
          popular: false,
        },
      ],
    },
  ]

  return (
    <main className="flex-1 py-20 bg-[#001a2c]">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h1 className="text-4xl md:text-5xl font-bold mb-4 text-white">Trading Challenges</h1>
          <p className="text-gray-400 max-w-2xl mx-auto">
            Choose the perfect funding plan for your trading style and goals. All plans include our industry-leading
            features and support.
          </p>
        </div>

        <Tabs defaultValue="step1" className="w-full">
          <div className="flex justify-center mb-10 overflow-x-auto pb-2">
            <TabsList className="bg-[#002a3c]">
              {accountTypes.map((type) => (
                <TabsTrigger
                  key={type.id}
                  value={type.id}
                  className="data-[state=active]:bg-teal-500 data-[state=active]:text-white px-6"
                >
                  {type.name}
                </TabsTrigger>
              ))}
            </TabsList>
          </div>

          {accountTypes.map((accountType) => (
            <TabsContent key={accountType.id} value={accountType.id}>
              <div className="text-center mb-8">
                <h3 className="text-2xl font-bold text-white mb-2">{accountType.name}</h3>
                <p className="text-gray-400">{accountType.description}</p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                {accountType.plans.map((plan, index) => (
                  <Card
                    key={index}
                    className={`bg-[#002a3c] border-[#003a4c] ${
                      plan.popular ? "border-teal-500 border-2" : "border-[#003a4c]"
                    } relative`}
                  >
                    {plan.popular && (
                      <div className="absolute top-0 right-0">
                        <div className="bg-teal-500 text-white text-xs font-bold px-3 py-1 transform translate-x-2 -translate-y-0">
                          POPULAR
                        </div>
                      </div>
                    )}
                    <CardHeader>
                      <CardTitle className="text-white">{accountType.name}</CardTitle>
                      <CardDescription className="text-gray-400">Trading Challenge</CardDescription>
                      <div className="mt-4">
                        <div className="text-3xl font-bold text-white">{plan.capital}</div>
                        <div className="text-teal-400 font-medium">{plan.price}</div>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div className="flex justify-between items-center pb-2 border-b border-[#003a4c]">
                          <span className="text-gray-400">Profit Target</span>
                          <span className="font-medium text-white">{plan.profitTarget}</span>
                        </div>
                        <div className="flex justify-between items-center pb-2 border-b border-[#003a4c]">
                          <span className="text-gray-400">Max Drawdown</span>
                          <span className="font-medium text-white">{plan.maxDrawdown}</span>
                        </div>
                        <div className="flex justify-between items-center pb-2 border-b border-[#003a4c]">
                          <span className="text-gray-400">Profit Split</span>
                          <span className="font-medium text-white">{plan.profitSplit}</span>
                        </div>
                      </div>

                      <ul className="space-y-3 mt-6">
                        {plan.features.map((feature, idx) => (
                          <li key={idx} className="flex items-start">
                            <Check className="h-5 w-5 text-teal-400 mr-2 shrink-0" />
                            <span className="text-gray-300 text-sm">{feature}</span>
                          </li>
                        ))}
                      </ul>
                    </CardContent>
                    <CardFooter>
                      <Button
                        className={`w-full ${
                          plan.popular
                            ? "bg-teal-500 hover:bg-teal-600 text-white"
                            : "bg-[#003a4c] text-white hover:bg-[#004a5c]"
                        }`}
                      >
                        Get Started
                      </Button>
                    </CardFooter>
                  </Card>
                ))}
              </div>
            </TabsContent>
          ))}
        </Tabs>
      </div>
    </main>
  )
}
